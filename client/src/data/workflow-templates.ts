/**
 * Plantillas de workflows predefinidas para Emma Studio
 * Workflows listos para usar que los usuarios pueden cargar y personalizar
 */

import { EmmaWorkflow } from '@/types/workflow-types';

export const WORKFLOW_TEMPLATES: Record<string, EmmaWorkflow> = {
  'basic-image-generation': {
    id: 'basic-image-generation',
    name: 'Generación Básica de Imagen',
    description: 'Crea una imagen desde texto y mejórala automáticamente',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'beautiful landscape, mountains, sunset, masterpiece'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'ideogram-1',
        type: 'emmaNode',
        position: { x: 400, y: 100 },
        data: {
          nodeType: 'ideogram-generator',
          definition: {} as any,
          inputs: {
            model: 'ideogram-3.0',
            aspect_ratio: '16:9',
            style: 'realistic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'upscale-1',
        type: 'emmaNode',
        position: { x: 700, y: 100 },
        data: {
          nodeType: 'upscale',
          definition: {} as any,
          inputs: {
            scale_factor: 2,
            creativity: 0.3
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 1000, y: 100 },
        data: {
          nodeType: 'image-output',
          definition: {} as any,
          inputs: {
            filename: 'landscape-hd',
            format: 'PNG'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'ideogram-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'ideogram-1',
        target: 'upscale-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      },
      {
        id: 'e3-4',
        source: 'upscale-1',
        target: 'output-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['imagen', 'básico', 'ideogram'],
      category: 'Generación de Imágenes'
    }
  },

  'dalle-background-removal': {
    id: 'dalle-background-removal',
    name: 'DALL-E + Fondo Transparente',
    description: 'Genera imagen con DALL-E y remueve el fondo automáticamente',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'cute robot character, white background, product shot'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'dalle-1',
        type: 'emmaNode',
        position: { x: 400, y: 100 },
        data: {
          nodeType: 'dalle-generator',
          definition: {} as any,
          inputs: {
            model: 'dall-e-3',
            size: '1024x1024',
            quality: 'hd'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'bg-remove-1',
        type: 'emmaNode',
        position: { x: 700, y: 100 },
        data: {
          nodeType: 'background-remover',
          definition: {} as any,
          inputs: {},
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 1000, y: 100 },
        data: {
          nodeType: 'image-output',
          definition: {} as any,
          inputs: {
            filename: 'robot-transparent',
            format: 'PNG'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'dalle-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'dalle-1',
        target: 'bg-remove-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      },
      {
        id: 'e3-4',
        source: 'bg-remove-1',
        target: 'output-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['dalle', 'fondo', 'transparente'],
      category: 'Edición de Imágenes'
    }
  },

  'style-transfer-workflow': {
    id: 'style-transfer-workflow',
    name: 'Transferencia de Estilo',
    description: 'Combina dos estilos diferentes en una imagen final',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 50 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'modern city skyline, architecture'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'text-input-2',
        type: 'emmaNode',
        position: { x: 100, y: 200 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'van gogh painting style, impressionist'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'ideogram-1',
        type: 'emmaNode',
        position: { x: 350, y: 50 },
        data: {
          nodeType: 'ideogram-generator',
          definition: {} as any,
          inputs: {
            model: 'ideogram-3.0',
            style: 'realistic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'dalle-1',
        type: 'emmaNode',
        position: { x: 350, y: 200 },
        data: {
          nodeType: 'dalle-generator',
          definition: {} as any,
          inputs: {
            model: 'dall-e-3',
            size: '1024x1024'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'style-transfer-1',
        type: 'emmaNode',
        position: { x: 600, y: 125 },
        data: {
          nodeType: 'style-transfer',
          definition: {} as any,
          inputs: {
            strength: 0.7
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 850, y: 125 },
        data: {
          nodeType: 'image-output',
          definition: {} as any,
          inputs: {
            filename: 'city-vangogh-style',
            format: 'PNG'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-3',
        source: 'text-input-1',
        target: 'ideogram-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-4',
        source: 'text-input-2',
        target: 'dalle-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e3-5',
        source: 'ideogram-1',
        target: 'style-transfer-1',
        sourceHandle: 'image',
        targetHandle: 'content_image'
      },
      {
        id: 'e4-5',
        source: 'dalle-1',
        target: 'style-transfer-1',
        sourceHandle: 'image',
        targetHandle: 'style_image'
      },
      {
        id: 'e5-6',
        source: 'style-transfer-1',
        target: 'output-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['estilo', 'artístico', 'combinación'],
      category: 'Arte y Estilo'
    }
  },

  'video-creation-workflow': {
    id: 'video-creation-workflow',
    name: 'Creación de Video',
    description: 'Genera imagen y convierte a video con movimiento',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 100, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'serene lake with mountains, golden hour'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'ideogram-1',
        type: 'emmaNode',
        position: { x: 350, y: 100 },
        data: {
          nodeType: 'ideogram-generator',
          definition: {} as any,
          inputs: {
            model: 'ideogram-3.0',
            aspect_ratio: '16:9',
            style: 'realistic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-1',
        type: 'emmaNode',
        position: { x: 600, y: 100 },
        data: {
          nodeType: 'video-generator',
          definition: {} as any,
          inputs: {
            prompt: 'camera slowly panning across the lake, gentle water movement',
            duration: '5',
            aspect_ratio: '16:9'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 850, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'lake-cinematic'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'ideogram-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'ideogram-1',
        target: 'video-1',
        sourceHandle: 'image',
        targetHandle: 'reference_image'
      },
      {
        id: 'e3-4',
        source: 'video-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['video', 'luma', 'cinematico'],
      category: 'Generación de Video'
    }
  },

  'simple-video-creation': {
    id: 'simple-video-creation',
    name: 'Creación Simple de Video',
    description: 'Workflow básico: genera video con texto y narración',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 50, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'Un océano con olas al atardecer, colores cálidos'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'simple-video-1',
        type: 'emmaNode',
        position: { x: 300, y: 100 },
        data: {
          nodeType: 'simple-video-generator',
          definition: {} as any,
          inputs: {
            video_text: 'RELAJACIÓN',
            voice_text: 'Disfruta de este momento de paz y tranquilidad',
            voice_gender: 'female'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 550, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'video-relajacion'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'simple-video-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'simple-video-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['video', 'simple', 'narración'],
      category: 'Video'
    }
  },

  'image-to-video-workflow': {
    id: 'image-to-video-workflow',
    name: 'Imagen a Video',
    description: 'Convierte imágenes en videos con movimiento natural',
    nodes: [
      {
        id: 'image-input-1',
        type: 'emmaNode',
        position: { x: 50, y: 100 },
        data: {
          nodeType: 'image-input',
          definition: {} as any,
          inputs: {},
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'image-to-video-1',
        type: 'emmaNode',
        position: { x: 300, y: 100 },
        data: {
          nodeType: 'image-to-video',
          definition: {} as any,
          inputs: {
            motion_bucket_id: 127,
            cfg_scale: 1.8,
            seed: 0
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-editor-1',
        type: 'emmaNode',
        position: { x: 550, y: 100 },
        data: {
          nodeType: 'video-editor',
          definition: {} as any,
          inputs: {
            operation: 'add_text',
            text_overlay: 'Video generado con Emma AI',
            brightness: 1.1,
            contrast: 1.2
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 800, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'imagen-a-video-final'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'image-input-1',
        target: 'image-to-video-1',
        sourceHandle: 'image',
        targetHandle: 'image'
      },
      {
        id: 'e2-3',
        source: 'image-to-video-1',
        target: 'video-editor-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e3-4',
        source: 'video-editor-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['imagen-a-video', 'movimiento', 'edición'],
      category: 'Video'
    }
  },

  'auto-video-generation': {
    id: 'auto-video-generation',
    name: 'Generación Automática',
    description: 'Workflow completo de generación automática de video',
    nodes: [
      {
        id: 'text-input-1',
        type: 'emmaNode',
        position: { x: 50, y: 100 },
        data: {
          nodeType: 'text-input',
          definition: {} as any,
          inputs: {
            text: 'Tutorial de cocina: cómo hacer pasta italiana'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-generator-1',
        type: 'emmaNode',
        position: { x: 300, y: 100 },
        data: {
          nodeType: 'video-generator',
          definition: {} as any,
          inputs: {
            style: 'tutorial',
            duration: 45,
            aspect_ratio: '16:9',
            include_music: 'yes',
            include_voiceover: 'yes'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'video-editor-1',
        type: 'emmaNode',
        position: { x: 550, y: 100 },
        data: {
          nodeType: 'video-editor',
          definition: {} as any,
          inputs: {
            operation: 'trim',
            start_time: 0,
            end_time: 30,
            brightness: 1.1,
            contrast: 1.1
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      },
      {
        id: 'output-1',
        type: 'emmaNode',
        position: { x: 800, y: 100 },
        data: {
          nodeType: 'video-output',
          definition: {} as any,
          inputs: {
            filename: 'tutorial-pasta-final'
          },
          outputs: {},
          isValid: true,
          errors: [],
          isExecuting: false,
          isCompleted: false
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'text-input-1',
        target: 'video-generator-1',
        sourceHandle: 'text',
        targetHandle: 'prompt'
      },
      {
        id: 'e2-3',
        source: 'video-generator-1',
        target: 'video-editor-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      },
      {
        id: 'e3-4',
        source: 'video-editor-1',
        target: 'output-1',
        sourceHandle: 'video',
        targetHandle: 'video'
      }
    ],
    metadata: {
      version: '1.0.0',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      tags: ['automático', 'tutorial', 'completo'],
      category: 'Video'
    }
  }
};

export const TEMPLATE_CATEGORIES = {
  'Generación de Imágenes': ['basic-image-generation'],
  'Edición de Imágenes': ['dalle-background-removal'],
  'Arte y Estilo': ['style-transfer-workflow'],
  'Generación de Video': ['video-creation-workflow'],
  'Video': ['simple-video-creation', 'image-to-video-workflow', 'auto-video-generation']
};
