/**
 * SEO & GPT Optimizer™ - Analytics Page
 * Main analytics dashboard with comprehensive metrics and insights
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Download, Share2, RefreshCw, Calendar } from 'lucide-react';
import { useLocation } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import LoadingSpinner from '../../components/seo-gpt-optimizer/shared/LoadingSpinner';
import ScoreHistory from '../../components/seo-gpt-optimizer/analytics/ScoreHistory';
import ProjectMetrics from '../../components/seo-gpt-optimizer/analytics/ProjectMetrics';
import CompetitorAnalysis from '../../components/seo-gpt-optimizer/analytics/CompetitorAnalysis';

interface AnalyticsData {
  scoreHistory: Array<{
    score: number;
    timestamp: string;
    version: number;
    trigger_event?: string;
  }>;
  competitorData: Array<{
    domain: string;
    title: string;
    url: string;
    estimated_score: number;
    position: number;
    content_length: number;
    authority_signals: number;
    semantic_similarity: number;
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
  }>;
}

const AnalyticsPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const { projects, loading: projectsLoading } = useProjects();
  
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false); // NO LOADING
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, selectedProjectId]);

  const loadAnalyticsData = async () => {
    try {
      // NO LOADING - Load instantly
      setError(null);

      // Mock data for demonstration
      const mockData: AnalyticsData = {
        scoreHistory: [
          { score: 65.2, timestamp: '2024-01-01T00:00:00Z', version: 1, trigger_event: 'Initial analysis' },
          { score: 68.5, timestamp: '2024-01-02T00:00:00Z', version: 2, trigger_event: 'Content update' },
          { score: 72.1, timestamp: '2024-01-03T00:00:00Z', version: 3, trigger_event: 'SEO optimization' },
          { score: 75.8, timestamp: '2024-01-04T00:00:00Z', version: 4, trigger_event: 'Authority improvement' },
          { score: 78.3, timestamp: '2024-01-05T00:00:00Z', version: 5, trigger_event: 'Content expansion' },
          { score: 81.2, timestamp: '2024-01-06T00:00:00Z', version: 6, trigger_event: 'Final optimization' }
        ],
        competitorData: [
          {
            domain: 'wikipedia.org',
            title: 'Comprehensive Guide to the Topic',
            url: 'https://wikipedia.org/example',
            estimated_score: 92.5,
            position: 1,
            content_length: 3200,
            authority_signals: 95,
            semantic_similarity: 88,
            strengths: ['High authority domain', 'Comprehensive coverage', 'Multiple citations'],
            weaknesses: ['Generic tone', 'Limited practical examples'],
            opportunities: ['Add more recent data', 'Include case studies']
          },
          {
            domain: 'expertsite.com',
            title: 'Expert Analysis and Insights',
            url: 'https://expertsite.com/example',
            estimated_score: 87.3,
            position: 2,
            content_length: 2800,
            authority_signals: 82,
            semantic_similarity: 91,
            strengths: ['Expert authorship', 'Technical depth', 'Recent updates'],
            weaknesses: ['Complex language', 'Limited accessibility'],
            opportunities: ['Simplify explanations', 'Add visual aids']
          },
          {
            domain: 'industry-blog.com',
            title: 'Practical Guide for Beginners',
            url: 'https://industry-blog.com/example',
            estimated_score: 79.1,
            position: 3,
            content_length: 2100,
            authority_signals: 75,
            semantic_similarity: 83,
            strengths: ['Practical examples', 'Clear structure', 'Beginner-friendly'],
            weaknesses: ['Limited depth', 'Few citations'],
            opportunities: ['Add expert quotes', 'Expand technical details']
          }
        ]
      };

      setAnalyticsData(mockData);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading analytics data');
    }
    // NO FINALLY LOADING
  };

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleRefresh = () => {
    loadAnalyticsData();
  };

  const handleExportData = () => {
    if (!analyticsData) return;

    const exportData = {
      exported_at: new Date().toISOString(),
      time_range: timeRange,
      project_id: selectedProjectId,
      score_history: analyticsData.scoreHistory,
      competitor_analysis: analyticsData.competitorData,
      projects_summary: projects.map(p => ({
        id: p.project_id,
        title: p.title,
        score: p.current_gpt_rank_score,
        status: p.status
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `seo-gpt-analytics-${timeRange}-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleShareAnalytics = async () => {
    if (navigator.share && analyticsData) {
      try {
        await navigator.share({
          title: 'SEO & GPT Optimizer™ Analytics',
          text: `Analytics report for ${timeRange} period`,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    }
  };

  const handleProjectClick = (projectId: string) => {
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/content-builder/${projectId}`);
  };

  const handleAnalyzeCompetitor = (url: string) => {
    // This would typically open a detailed competitor analysis
    console.log('Analyzing competitor:', url);
    // For now, just open the URL
    window.open(url, '_blank');
  };

  // Calculate current user score for competitor comparison
  const currentUserScore = selectedProjectId 
    ? projects.find(p => p.project_id === selectedProjectId)?.current_gpt_rank_score || 0
    : projects.length > 0 
      ? projects.reduce((sum, p) => sum + p.current_gpt_rank_score, 0) / projects.length 
      : 0;

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
                  <p className="text-gray-600 text-sm">
                    Análisis detallado de rendimiento y competencia
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Project Selector */}
                <select
                  value={selectedProjectId || ''}
                  onChange={(e) => setSelectedProjectId(e.target.value || null)}
                  className="px-3 py-2 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Todos los proyectos</option>
                  {projects.map(project => (
                    <option key={project.project_id} value={project.project_id}>
                      {project.title}
                    </option>
                  ))}
                </select>

                <button
                  onClick={handleRefresh}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                  title="Actualizar datos"
                >
                  <RefreshCw className="w-5 h-5" />
                </button>

                <button
                  onClick={handleExportData}
                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <Download className="w-4 h-4" />
                  Exportar
                </button>

                {navigator.share && (
                  <button
                    onClick={handleShareAnalytics}
                    className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                  >
                    <Share2 className="w-4 h-4" />
                    Compartir
                  </button>
                )}
              </div>
            </div>

            {/* Last Updated */}
            <div className="flex items-center gap-2 mt-4 text-sm text-gray-500">
              <Calendar className="w-4 h-4" />
              <span>Última actualización: {lastUpdated.toLocaleString()}</span>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* NO LOADING STATE */}

          {/* Error State */}
          {error && (
            <motion.div
              className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-red-600 font-bold">!</span>
                </div>
                <div>
                  <h3 className="font-semibold text-red-900">Error al cargar analytics</h3>
                  <p className="text-red-700 text-sm mt-1">{error}</p>
                </div>
                <button
                  onClick={handleRefresh}
                  className="ml-auto text-red-600 hover:text-red-700"
                >
                  <RefreshCw className="w-5 h-5" />
                </button>
              </div>
            </motion.div>
          )}

          {/* Analytics Content - NO LOADING */}
          {!error && analyticsData && (
            <div className="space-y-8">
              {/* Score History */}
              <ScoreHistory
                projectId={selectedProjectId || undefined}
                data={analyticsData.scoreHistory}
                timeRange={timeRange}
                onTimeRangeChange={setTimeRange}
              />

              {/* Project Metrics */}
              <ProjectMetrics
                projects={selectedProjectId 
                  ? projects.filter(p => p.project_id === selectedProjectId)
                  : projects
                }
                timeRange={timeRange}
                onProjectClick={handleProjectClick}
              />

              {/* Competitor Analysis */}
              <CompetitorAnalysis
                topic={selectedProjectId 
                  ? projects.find(p => p.project_id === selectedProjectId)?.topic || 'Selected Topic'
                  : 'All Projects'
                }
                userScore={currentUserScore}
                competitors={analyticsData.competitorData}
                onAnalyzeCompetitor={handleAnalyzeCompetitor}
              />
            </div>
          )}

          {/* Empty State - NO LOADING */}
          {!error && !analyticsData && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Sin datos de analytics
              </h3>
              <p className="text-gray-600 mb-6">
                Los datos de analytics aparecerán aquí una vez que tengas proyectos activos.
              </p>
              <button
                onClick={() => setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
              >
                Crear Primer Proyecto
              </button>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default AnalyticsPage;
