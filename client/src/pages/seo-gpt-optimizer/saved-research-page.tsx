/**
 * SEO & GPT Optimizer™ - Saved Research Page
 * Main orchestrator component for saved research functionality
 */

import React from 'react';
import { SavedResearchProvider } from '../../components/seo-gpt-optimizer/saved-research/context/SavedResearchContext';
import SavedResearchContainer from '../../components/seo-gpt-optimizer/saved-research/SavedResearchContainer';

const SavedResearchPage: React.FC = () => {
  return (
    <SavedResearchProvider>
      <SavedResearchContainer />
    </SavedResearchProvider>
  );
};

export default SavedResearchPage;
