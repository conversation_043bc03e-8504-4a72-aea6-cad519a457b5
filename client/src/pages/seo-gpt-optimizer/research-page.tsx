/**
 * SEO & GPT Optimizer™ - Research Page
 * Main page for conducting research
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Download, Share2, Save, Check } from 'lucide-react';
import { useLocation } from 'wouter';

import { useResearch } from '../../hooks/seo-gpt-optimizer/useResearch';
import { useSavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import ResearchForm from '../../components/seo-gpt-optimizer/research-engine/ResearchForm';
import ResearchResults from '../../components/seo-gpt-optimizer/research-engine/ResearchResults';

const ResearchPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [isSaved, setIsSaved] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Get project ID from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const projectId = urlParams.get('project');
  const {
    researchResults,
    loading,
    error,
    conductResearch,
    clearResults,
    clearError,
    hasResults,
    researchConfidence,
    processingTime
  } = useResearch();

  const { saveResearch } = useSavedResearch();

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleNewResearch = () => {
    clearResults();
    clearError();
    setIsSaved(false);
  };

  const handleSaveResearch = async () => {
    if (researchResults) {
      try {
        console.log('🔍 GUARDANDO INVESTIGACIÓN - Datos que se van a guardar:');
        console.log('📊 Topic:', researchResults.topic);
        console.log('📊 Intent Analysis:', researchResults.intent_analysis);
        console.log('📊 Google Results:', researchResults.google_results);
        console.log('📊 Questions:', researchResults.entities_and_questions);
        console.log('📊 Social Insights:', researchResults.social_insights);
        console.log('📊 Content Opportunities:', researchResults.content_opportunities);
        console.log('📊 Research Summary:', researchResults.research_summary);
        console.log('📊 DATOS COMPLETOS:', researchResults);

        await saveResearch({
          topic: researchResults.topic,
          results: researchResults, // GUARDANDO EXACTAMENTE LO QUE VES EN PANTALLA
          confidence: researchConfidence,
          processingTime: processingTime,
          projectId: projectId || undefined
        });

        console.log('✅ INVESTIGACIÓN GUARDADA EXITOSAMENTE');
        setIsSaved(true);

        // Mostrar mensaje temporal
        setTimeout(() => setIsSaved(false), 5000);
      } catch (error) {
        console.error('❌ Error al guardar investigación:', error);
      }
    } else {
      console.log('⚠️ No hay datos de investigación para guardar');
    }
  };

  const handleExportResults = async () => {
    if (!researchResults) return;

    setIsExporting(true);

    try {
      // Importar jsPDF dinámicamente
      const { default: jsPDF } = await import('jspdf');

      // Crear PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      let yPosition = 20;

      // Función para añadir nueva página si es necesario
      const checkPageBreak = (requiredSpace: number) => {
        if (yPosition + requiredSpace > pageHeight - 20) {
          pdf.addPage();
          yPosition = 20;
        }
      };

      // Función para añadir texto con salto de línea automático
      const addWrappedText = (text: string, x: number, y: number, maxWidth: number, lineHeight: number = 6) => {
        const lines = pdf.splitTextToSize(text, maxWidth);
        pdf.text(lines, x, y);
        return lines.length * lineHeight;
      };

      // Fecha actual
      const currentDate = new Date().toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

      // HEADER
      pdf.setFillColor(48, 24, 239); // #3018ef
      pdf.rect(0, 0, pageWidth, 40, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Investigacion de Contenido', pageWidth / 2, 20, { align: 'center' });

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Analisis completo para: ${researchResults.topic}`, pageWidth / 2, 28, { align: 'center' });
      pdf.text(`Generado el ${currentDate}`, pageWidth / 2, 35, { align: 'center' });

      yPosition = 50;

      // METRICAS
      checkPageBreak(25);
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Metricas de la Investigacion', 20, yPosition);
      yPosition += 10;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`• Confianza: ${(researchConfidence * 100).toFixed(1)}%`, 25, yPosition);
      yPosition += 6;
      pdf.text(`• Tiempo de procesamiento: ${processingTime.toFixed(1)} segundos`, 25, yPosition);
      yPosition += 6;
      pdf.text(`• Fuentes analizadas: ${(researchResults as any).research_quality_metrics?.data_sources_analyzed || 'N/A'}`, 25, yPosition);
      yPosition += 6;
      pdf.text(`• Resultados de busqueda: ${researchResults.google_results?.total_results || 0}`, 25, yPosition);
      yPosition += 15;

      // ANALISIS DE INTENCION
      checkPageBreak(50);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(48, 24, 239);
      pdf.text('Analisis de Intencion', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Tipo de Intencion:', 25, yPosition);
      yPosition += 6;
      pdf.setFont('helvetica', 'normal');
      const intentHeight = addWrappedText(researchResults.intent_analysis?.intent_type || 'No disponible', 30, yPosition, pageWidth - 60);
      yPosition += intentHeight + 5;

      pdf.setFont('helvetica', 'bold');
      pdf.text('Audiencia Objetivo:', 25, yPosition);
      yPosition += 6;
      pdf.setFont('helvetica', 'normal');
      const audienceHeight = addWrappedText(researchResults.intent_analysis?.target_audience || 'No disponible', 30, yPosition, pageWidth - 60);
      yPosition += audienceHeight + 5;

      // Informacion adicional del analisis de intencion
      if (researchResults.intent_analysis?.optimal_content_length) {
        pdf.setFont('helvetica', 'bold');
        pdf.text('Longitud Optima de Contenido:', 25, yPosition);
        yPosition += 6;
        pdf.setFont('helvetica', 'normal');
        const lengthHeight = addWrappedText(researchResults.intent_analysis.optimal_content_length, 30, yPosition, pageWidth - 60);
        yPosition += lengthHeight + 5;
      }

      if (researchResults.intent_analysis?.preferred_tone) {
        pdf.setFont('helvetica', 'bold');
        pdf.text('Tono Preferido:', 25, yPosition);
        yPosition += 6;
        pdf.setFont('helvetica', 'normal');
        const toneHeight = addWrappedText(researchResults.intent_analysis.preferred_tone, 30, yPosition, pageWidth - 60);
        yPosition += toneHeight + 5;
      }

      yPosition += 10;

      // PREGUNTAS FRECUENTES
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(221, 58, 90); // #dd3a5a
      pdf.text('Preguntas Frecuentes', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'normal');
      const questions = researchResults.entities_and_questions?.common_questions?.slice(0, 8) || ['No se encontraron preguntas'];
      questions.forEach((question: string) => {
        checkPageBreak(8);
        const questionHeight = addWrappedText(`• ${question}`, 25, yPosition, pageWidth - 50);
        yPosition += questionHeight + 2;
      });
      yPosition += 10;

      // RESULTADOS DE GOOGLE
      checkPageBreak(50);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(66, 133, 244); // Google blue
      pdf.text('Resultados de Google', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Total de resultados analizados: ${researchResults.google_results?.results?.length || 0}`, 25, yPosition);
      yPosition += 8;

      // Top 5 resultados de Google
      if (researchResults.google_results?.results?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Top 5 Resultados Competidores:', 25, yPosition);
        yPosition += 8;

        researchResults.google_results.results.slice(0, 5).forEach((result: any, index: number) => {
          checkPageBreak(20);
          pdf.setFontSize(10);
          pdf.setFont('helvetica', 'bold');
          pdf.setTextColor(48, 24, 239);
          pdf.text(`${index + 1}. ${result.domain || 'Dominio no disponible'}`, 30, yPosition);
          yPosition += 5;

          pdf.setFont('helvetica', 'normal');
          pdf.setTextColor(0, 0, 0);
          const titleHeight = addWrappedText(result.title || 'Título no disponible', 35, yPosition, pageWidth - 70, 4);
          yPosition += titleHeight + 2;

          if (result.snippet) {
            const snippetHeight = addWrappedText(`"${result.snippet}"`, 35, yPosition, pageWidth - 70, 4);
            yPosition += snippetHeight + 3;
          }

          pdf.setFontSize(9);
          pdf.setTextColor(100, 100, 100);
          pdf.text(`Posicion: #${result.position || 'N/A'} | Titulo: ${result.title_length || 0} chars | Snippet: ${result.snippet_length || 0} chars`, 35, yPosition);
          yPosition += 8;
        });
      }

      // People Also Ask
      if (researchResults.google_results?.serp_features?.people_also_ask?.length > 0) {
        checkPageBreak(30);
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(221, 58, 90);
        pdf.text('La Gente Tambien Pregunta:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(0, 0, 0);
        researchResults.google_results.serp_features.people_also_ask.slice(0, 8).forEach((item: any) => {
          checkPageBreak(6);
          const questionHeight = addWrappedText(`• ${item.question || item}`, 30, yPosition, pageWidth - 60, 4);
          yPosition += questionHeight + 2;
        });
        yPosition += 10;
      }

      // INSIGHTS SOCIALES
      if (researchResults.social_insights?.reddit || researchResults.social_insights?.quora) {
        checkPageBreak(40);
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(255, 69, 0); // Reddit orange
        pdf.text('Insights Sociales', 20, yPosition);
        yPosition += 12;

        // Reddit insights
        if (researchResults.social_insights.reddit?.insights?.length > 0) {
          pdf.setFontSize(12);
          pdf.setFont('helvetica', 'bold');
          pdf.setTextColor(255, 69, 0);
          pdf.text(`Reddit (${researchResults.social_insights.reddit.total_results || 0} resultados)`, 25, yPosition);
          yPosition += 8;

          pdf.setFontSize(10);
          pdf.setFont('helvetica', 'normal');
          pdf.setTextColor(0, 0, 0);
          researchResults.social_insights.reddit.insights.slice(0, 3).forEach((insight: any) => {
            checkPageBreak(15);
            pdf.setFont('helvetica', 'bold');
            const titleHeight = addWrappedText(insight.title || 'Titulo no disponible', 30, yPosition, pageWidth - 60, 4);
            yPosition += titleHeight + 2;

            pdf.setFont('helvetica', 'normal');
            if (insight.snippet) {
              const snippetHeight = addWrappedText(insight.snippet, 30, yPosition, pageWidth - 60, 4);
              yPosition += snippetHeight + 2;
            }

            if (insight.subreddit) {
              pdf.setFontSize(9);
              pdf.setTextColor(100, 100, 100);
              pdf.text(`Subreddit: r/${insight.subreddit}`, 30, yPosition);
              yPosition += 5;
            }
            yPosition += 3;
          });
        }

        // Quora insights
        if (researchResults.social_insights.quora?.insights?.length > 0) {
          checkPageBreak(30);
          pdf.setFontSize(12);
          pdf.setFont('helvetica', 'bold');
          pdf.setTextColor(185, 43, 39); // Quora red
          pdf.text(`Quora (${researchResults.social_insights.quora.total_results || 0} resultados)`, 25, yPosition);
          yPosition += 8;

          pdf.setFontSize(10);
          pdf.setFont('helvetica', 'normal');
          pdf.setTextColor(0, 0, 0);
          researchResults.social_insights.quora.insights.slice(0, 3).forEach((insight: any) => {
            checkPageBreak(12);
            pdf.setFont('helvetica', 'bold');
            const questionHeight = addWrappedText(insight.question || 'Pregunta no disponible', 30, yPosition, pageWidth - 60, 4);
            yPosition += questionHeight + 2;

            pdf.setFont('helvetica', 'normal');
            if (insight.answer_preview) {
              const answerHeight = addWrappedText(insight.answer_preview, 30, yPosition, pageWidth - 60, 4);
              yPosition += answerHeight + 5;
            }
          });
        }
        yPosition += 10;
      }

      // OPORTUNIDADES DE CONTENIDO
      checkPageBreak(40);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(16, 185, 129); // #10b981
      pdf.text('Oportunidades de Contenido', 20, yPosition);
      yPosition += 12;

      // Gaps de contenido
      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Gaps de Contenido Identificados:', 25, yPosition);
      yPosition += 8;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      const gaps = researchResults.content_opportunities?.content_gaps?.slice(0, 5) || ['No se identificaron gaps especificos'];
      gaps.forEach((gap: string) => {
        checkPageBreak(8);
        const gapHeight = addWrappedText(`• ${gap}`, 30, yPosition, pageWidth - 60);
        yPosition += gapHeight + 2;
      });
      yPosition += 8;

      // Palabras clave
      checkPageBreak(15);
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Palabras Clave Objetivo:', 25, yPosition);
      yPosition += 6;
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      const keywords = researchResults.content_opportunities?.target_keywords?.slice(0, 10).join(', ') || 'No disponibles';
      const keywordsHeight = addWrappedText(keywords, 30, yPosition, pageWidth - 60);
      yPosition += keywordsHeight + 8;

      // Puntuacion
      checkPageBreak(10);
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Puntuacion de Oportunidad:', 25, yPosition);
      yPosition += 6;
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`${researchResults.content_opportunities?.opportunity_score || 0}/100`, 30, yPosition);
      yPosition += 15;

      // RESUMEN EJECUTIVO
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(48, 24, 239);
      pdf.text('Resumen Ejecutivo', 20, yPosition);
      yPosition += 12;

      // Insights clave
      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Insights Clave:', 25, yPosition);
      yPosition += 8;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      const insights = researchResults.research_summary?.key_insights || ['No disponibles'];
      insights.forEach((insight: string) => {
        checkPageBreak(8);
        const insightHeight = addWrappedText(`• ${insight}`, 30, yPosition, pageWidth - 60);
        yPosition += insightHeight + 2;
      });
      yPosition += 8;

      // Proximos pasos recomendados
      checkPageBreak(15);
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Proximos Pasos Recomendados:', 25, yPosition);
      yPosition += 8;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      const nextSteps = researchResults.research_summary?.priority_questions || [
        'Crear contenido basado en las preguntas frecuentes identificadas',
        'Optimizar para las palabras clave objetivo encontradas',
        'Analizar la competencia en los primeros resultados de Google',
        'Desarrollar contenido que llene los gaps identificados',
        'Implementar el tono y longitud recomendados'
      ];
      nextSteps.forEach((step: string) => {
        checkPageBreak(8);
        const stepHeight = addWrappedText(`• ${step}`, 30, yPosition, pageWidth - 60);
        yPosition += stepHeight + 2;
      });
      yPosition += 10;

      // PALABRAS CLAVE PRIORITARIAS
      if (researchResults.research_summary?.priority_keywords?.length > 0) {
        checkPageBreak(25);
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(147, 51, 234); // Purple
        pdf.text('Palabras Clave Prioritarias', 20, yPosition);
        yPosition += 12;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(0, 0, 0);
        researchResults.research_summary.priority_keywords.slice(0, 15).forEach((keyword: string, index: number) => {
          checkPageBreak(6);
          pdf.text(`${index + 1}. ${keyword}`, 25, yPosition);
          yPosition += 5;
        });
        yPosition += 10;
      }

      // ENFOQUE RECOMENDADO
      if (researchResults.research_summary?.recommended_approach) {
        checkPageBreak(30);
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(59, 130, 246); // Blue
        pdf.text('Enfoque Recomendado', 20, yPosition);
        yPosition += 12;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(0, 0, 0);

        Object.entries(researchResults.research_summary.recommended_approach).forEach(([key, value]) => {
          checkPageBreak(10);
          pdf.setFont('helvetica', 'bold');
          pdf.text(`${key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')}:`, 25, yPosition);
          yPosition += 6;
          pdf.setFont('helvetica', 'normal');
          const valueHeight = addWrappedText(String(value), 30, yPosition, pageWidth - 60);
          yPosition += valueHeight + 5;
        });
        yPosition += 10;
      }

      // METRICAS DE CALIDAD
      if ((researchResults as any).research_quality_metrics) {
        checkPageBreak(25);
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(34, 197, 94); // Green
        pdf.text('Metricas de Calidad de la Investigacion', 20, yPosition);
        yPosition += 12;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(0, 0, 0);

        const metrics = (researchResults as any).research_quality_metrics;
        Object.entries(metrics).forEach(([key, value]) => {
          checkPageBreak(6);
          pdf.text(`• ${key.replace(/_/g, ' ').charAt(0).toUpperCase() + key.replace(/_/g, ' ').slice(1)}: ${value}`, 25, yPosition);
          yPosition += 5;
        });
        yPosition += 10;
      }

      // RECOMENDACIONES FINALES
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(239, 68, 68); // Red
      pdf.text('Recomendaciones Finales', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);

      const finalRecommendations = [
        'Prioriza la creacion de contenido que responda directamente a las preguntas mas frecuentes',
        'Utiliza un enfoque de contenido en pilares basado en los temas principales identificados',
        'Optimiza la longitud del contenido segun las recomendaciones del analisis de intencion',
        'Implementa una estrategia de palabras clave de cola larga basada en las oportunidades encontradas',
        'Monitorea regularmente a los competidores identificados en los primeros resultados',
        'Considera crear contenido multimedia para diferenciarte de la competencia',
        'Implementa schema markup para mejorar la visibilidad en los resultados de busqueda'
      ];

      finalRecommendations.forEach((recommendation: string) => {
        checkPageBreak(8);
        const recHeight = addWrappedText(`• ${recommendation}`, 25, yPosition, pageWidth - 50);
        yPosition += recHeight + 3;
      });

      // FOOTER
      checkPageBreak(20);
      yPosition = pageHeight - 30;
      pdf.setFillColor(30, 41, 59); // #1e293b
      pdf.rect(0, yPosition - 5, pageWidth, 25, 'F');

      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.text('Generado por SEO & GPT Optimizer - Emma Studio', pageWidth / 2, yPosition + 5, { align: 'center' });
      pdf.text('Este reporte contiene analisis basado en IA y datos de multiples fuentes', pageWidth / 2, yPosition + 12, { align: 'center' });

      // Descargar PDF
      const fileName = `investigacion-${researchResults.topic.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.pdf`;
      pdf.save(fileName);

    } catch (error) {
      console.error('Error al exportar PDF:', error);
      // Fallback: descargar como JSON si falla
      const dataStr = JSON.stringify(researchResults, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `research-${researchResults.topic}-${Date.now()}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } finally {
      setIsExporting(false);
    }
  };

  const handleShareResults = async () => {
    if (researchResults && navigator.share) {
      try {
        await navigator.share({
          title: `Investigación: ${researchResults.topic}`,
          text: `Resultados de investigación para "${researchResults.topic}" - Confianza: ${(researchConfidence * 100).toFixed(1)}%`,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    }
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Research Engine</h1>
                  <p className="text-gray-600 text-sm">
                    Investiga temas y encuentra oportunidades de contenido
                    {projectId && (
                      <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                        📁 Para proyecto específico
                      </span>
                    )}
                  </p>
                </div>
              </div>

              {hasResults && (
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <button
                      onClick={handleSaveResearch}
                      className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 ${
                        isSaved
                          ? 'bg-green-100 text-green-700 border border-green-200'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {isSaved ? (
                        <>
                          <Check className="w-4 h-4" />
                          Guardado
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4" />
                          Guardar
                        </>
                      )}
                    </button>

                    {isSaved && (
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg z-10 whitespace-nowrap">
                        ✓ Investigación guardada exitosamente
                        <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-green-600 rotate-45"></div>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={handleExportResults}
                    disabled={isExporting}
                    className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200 disabled:opacity-50"
                  >
                    <Download className="w-4 h-4" />
                    {isExporting ? 'Descargando...' : 'Descargar'}
                  </button>
                  
                  {typeof navigator !== 'undefined' && navigator.share && (
                    <button
                      onClick={handleShareResults}
                      className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                    >
                      <Share2 className="w-4 h-4" />
                      Compartir
                    </button>
                  )}
                  
                  <button
                    onClick={handleNewResearch}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                  >
                    Nueva Investigación
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Error Display */}
          {error.hasError && (
            <motion.div
              className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-red-600 font-bold">!</span>
                </div>
                <div>
                  <h3 className="font-semibold text-red-900">Error en la investigación</h3>
                  <p className="text-red-700 text-sm mt-1">{error.message}</p>
                </div>
                <button
                  onClick={clearError}
                  className="ml-auto text-red-600 hover:text-red-700"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}

          {/* Loading State */}
          {loading.isLoading && (
            <motion.div
              className="max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {loading.message}
                  </h3>

                  <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <motion.div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${loading.progress || 0}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>

                  <p className="text-gray-600 text-sm">
                    {loading.progress || 0}% completado - Esto puede tomar 30-60 segundos
                  </p>

                  <div className="mt-6 grid grid-cols-2 gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${(loading.progress || 0) > 20 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      Análisis de intención
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${(loading.progress || 0) > 40 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      Búsqueda en Google
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${(loading.progress || 0) > 60 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      Insights sociales
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${(loading.progress || 0) > 80 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      Análisis final
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Research Form */}
          {!hasResults && !loading.isLoading && (
            <div className="max-w-2xl mx-auto">
              <ResearchForm
                onSubmit={conductResearch}
                loading={loading.isLoading}
              />
              
              {/* Tips */}
              <motion.div
                className="mt-8 bg-white rounded-2xl shadow-sm border border-gray-100 p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <h3 className="font-semibold text-gray-900 mb-4">
                  💡 Tips para mejores resultados
                </h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <strong>Sé específico:</strong> En lugar de "marketing", usa "marketing digital para pequeñas empresas"
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <strong>Incluye contexto:</strong> "beneficios del yoga para principiantes" es mejor que solo "yoga"
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <strong>Piensa en tu audiencia:</strong> Considera qué buscarían tus usuarios objetivo
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          )}

          {/* Research Results - NO LOADING */}
          {hasResults && researchResults && (
            <ResearchResults
              results={researchResults}
              confidence={researchConfidence}
              processingTime={processingTime}
            />
          )}

          {/* Success Summary - NO LOADING */}
          {hasResults && (
            <motion.div
              className="mt-8 bg-green-50 border border-green-200 rounded-2xl p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold">✓</span>
                </div>
                <div>
                  <h3 className="font-semibold text-green-900">
                    Investigación completada exitosamente
                  </h3>
                  <p className="text-green-700 text-sm mt-1">
                    Procesado en {processingTime.toFixed(1)}s con {(researchConfidence * 100).toFixed(1)}% de confianza
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default ResearchPage;
