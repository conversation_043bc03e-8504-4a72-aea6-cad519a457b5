/**
 * SEO & GPT Optimizer™ - Research Management Page
 * Página para gestionar y organizar todas las investigaciones
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  FolderOpen,
  Folder,
  Archive,
  Trash2,
  Edit,
  Download,
  Calendar,
  Target
} from 'lucide-react';
import { useLocation } from 'wouter';

import { useSavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';
import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';

const ResearchManagementPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const { savedResearch, deleteResearch, moveResearchToProject, bulkMoveResearch } = useSavedResearch();
  const { projects } = useProjects();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [filterProject, setFilterProject] = useState<string>('all');
  const [selectedResearch, setSelectedResearch] = useState<string[]>([]);
  const [showMoveModal, setShowMoveModal] = useState(false);

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  // Filtrar investigaciones
  const filteredResearch = savedResearch.filter(research => {
    const matchesSearch = research.topic.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesProject = filterProject === 'all' || 
                          (filterProject === 'unassigned' && !research.projectId) ||
                          research.projectId === filterProject;
    return matchesSearch && matchesProject;
  });

  // Agrupar por proyecto
  const groupedResearch = {
    unassigned: filteredResearch.filter(r => !r.projectId),
    byProject: projects.reduce((acc, project) => {
      acc[project.project_id] = filteredResearch.filter(r => r.projectId === project.project_id);
      return acc;
    }, {} as Record<string, typeof filteredResearch>)
  };

  const handleSelectResearch = (researchId: string) => {
    setSelectedResearch(prev => 
      prev.includes(researchId) 
        ? prev.filter(id => id !== researchId)
        : [...prev, researchId]
    );
  };

  const handleDeleteSelected = async () => {
    if (selectedResearch.length === 0) return;
    
    const confirmed = window.confirm(`¿Eliminar ${selectedResearch.length} investigación(es)?`);
    if (!confirmed) return;

    for (const researchId of selectedResearch) {
      await deleteResearch(researchId);
    }
    setSelectedResearch([]);
  };

  const handleViewResearch = (researchId: string) => {
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/saved-research?highlight=${researchId}`);
  };

  const handleMoveToProject = async (projectId: string | null) => {
    if (selectedResearch.length === 0) return;

    const success = await bulkMoveResearch(selectedResearch, projectId);
    if (success) {
      setSelectedResearch([]);
      setShowMoveModal(false);
    }
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Gestión de Investigaciones</h1>
                  <p className="text-gray-600 text-sm">
                    Organiza y administra todas tus investigaciones guardadas
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {selectedResearch.length > 0 && (
                  <>
                    <button
                      onClick={() => setShowMoveModal(true)}
                      className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-colors duration-200"
                    >
                      <FolderOpen className="w-4 h-4" />
                      Mover a Proyecto ({selectedResearch.length})
                    </button>
                    <button
                      onClick={handleDeleteSelected}
                      className="flex items-center gap-2 px-4 py-2 text-red-600 hover:bg-red-50 rounded-xl transition-colors duration-200"
                    >
                      <Trash2 className="w-4 h-4" />
                      Eliminar ({selectedResearch.length})
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filtros y Búsqueda */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Búsqueda */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Buscar investigaciones..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Filtro por proyecto */}
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <select
                  value={filterProject}
                  onChange={(e) => setFilterProject(e.target.value)}
                  className="pl-10 pr-8 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white min-w-[200px]"
                >
                  <option value="all">Todos los proyectos</option>
                  <option value="unassigned">Sin asignar</option>
                  {projects.map(project => (
                    <option key={project.project_id} value={project.project_id}>
                      {project.title}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Estadísticas */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-blue-600">{savedResearch.length}</div>
                <div className="text-sm text-blue-700">Total Investigaciones</div>
              </div>
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-gray-600">{groupedResearch.unassigned.length}</div>
                <div className="text-sm text-gray-700">Sin Asignar</div>
              </div>
              <div className="bg-green-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-green-600">
                  {Object.values(groupedResearch.byProject).reduce((sum, arr) => sum + arr.length, 0)}
                </div>
                <div className="text-sm text-green-700">En Proyectos</div>
              </div>
              <div className="bg-purple-50 rounded-xl p-4">
                <div className="text-2xl font-bold text-purple-600">{filteredResearch.length}</div>
                <div className="text-sm text-purple-700">Mostrando</div>
              </div>
            </div>
          </div>

          {/* Lista de Investigaciones */}
          <div className="space-y-6">
            {/* Investigaciones sin asignar */}
            {groupedResearch.unassigned.length > 0 && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center gap-3">
                    <Archive className="w-5 h-5 text-gray-600" />
                    <h3 className="font-semibold text-gray-900">
                      Sin Asignar ({groupedResearch.unassigned.length})
                    </h3>
                  </div>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {groupedResearch.unassigned.map((research) => (
                      <ResearchCard
                        key={research.id}
                        research={research}
                        isSelected={selectedResearch.includes(research.id)}
                        onSelect={() => handleSelectResearch(research.id)}
                        onView={() => handleViewResearch(research.id)}
                        projects={projects}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Investigaciones por proyecto */}
            {projects.map(project => {
              const projectResearch = groupedResearch.byProject[project.project_id] || [];
              if (projectResearch.length === 0) return null;

              return (
                <div key={project.project_id} className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                  <div className="bg-blue-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center gap-3">
                      <FolderOpen className="w-5 h-5 text-blue-600" />
                      <h3 className="font-semibold text-gray-900">
                        {project.title} ({projectResearch.length})
                      </h3>
                      <span className="text-sm text-gray-500">
                        {project.topic}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      {projectResearch.map((research) => (
                        <ResearchCard
                          key={research.id}
                          research={research}
                          isSelected={selectedResearch.includes(research.id)}
                          onSelect={() => handleSelectResearch(research.id)}
                          onView={() => handleViewResearch(research.id)}
                          projects={projects}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Estado vacío */}
          {filteredResearch.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron investigaciones
              </h3>
              <p className="text-gray-600">
                {searchQuery || filterProject !== 'all' 
                  ? 'Intenta ajustar los filtros de búsqueda'
                  : 'Aún no tienes investigaciones guardadas'
                }
              </p>
            </div>
          )}
        </div>

        {/* Modal para mover investigaciones */}
        {showMoveModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Mover {selectedResearch.length} investigación(es)
              </h3>

              <div className="space-y-3 mb-6">
                <button
                  onClick={() => handleMoveToProject(null)}
                  className="w-full text-left p-3 rounded-xl hover:bg-gray-50 transition-colors duration-200 flex items-center gap-3"
                >
                  <Archive className="w-5 h-5 text-gray-600" />
                  <div>
                    <div className="font-medium text-gray-900">Sin Asignar</div>
                    <div className="text-sm text-gray-500">Mover a investigaciones generales</div>
                  </div>
                </button>

                {projects.map(project => (
                  <button
                    key={project.project_id}
                    onClick={() => handleMoveToProject(project.project_id)}
                    className="w-full text-left p-3 rounded-xl hover:bg-blue-50 transition-colors duration-200 flex items-center gap-3"
                  >
                    <Folder className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900">{project.title}</div>
                      <div className="text-sm text-gray-500">{project.topic}</div>
                    </div>
                  </button>
                ))}
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowMoveModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
};

// Componente para cada tarjeta de investigación
interface ResearchCardProps {
  research: any;
  isSelected: boolean;
  onSelect: () => void;
  onView: () => void;
  projects: any[];
}

const ResearchCard: React.FC<ResearchCardProps> = ({
  research,
  isSelected,
  onSelect,
  onView,
  projects
}) => {
  return (
    <motion.div
      className={`border rounded-xl p-4 cursor-pointer transition-all duration-200 ${
        isSelected 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
      }`}
      onClick={onSelect}
      whileHover={{ scale: 1.01 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
            onClick={(e) => e.stopPropagation()}
          />
          <div>
            <h4 className="font-semibold text-gray-900">{research.topic}</h4>
            <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
              <span className="flex items-center gap-1">
                <Target className="w-3 h-3" />
                {(research.confidence * 100).toFixed(0)}% confianza
              </span>
              <span className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {new Date(research.savedAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onView();
            }}
            className="p-2 text-gray-400 hover:text-blue-600 rounded-lg transition-colors duration-200"
          >
            <Edit className="w-4 h-4" />
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default ResearchManagementPage;
