/**
 * SEO & GPT Optimizer™ - Project Dashboard Page
 * Dashboard individual para cada proyecto con todos sus contenidos
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Plus, 
  FileText, 
  Search, 
  BarChart3,
  Folder,
  Calendar,
  Target,
  TrendingUp,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { useLocation, useRoute } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { useSavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import LoadingSpinner from '../../components/seo-gpt-optimizer/shared/LoadingSpinner';

interface ProjectContent {
  id: string;
  title: string;
  type: 'blog' | 'research';
  status: 'draft' | 'completed' | 'published';
  word_count: number;
  gpt_rank_score: number;
  created_at: string;
  updated_at: string;
}

const ProjectDashboardPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [match, params] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/project/:projectId');
  const projectId = params?.projectId;

  const { currentProject, loadProject, loading } = useProjects();
  const { getResearchByProject } = useSavedResearch();
  const [projectContents, setProjectContents] = useState<ProjectContent[]>([]);
  const [activeTab, setActiveTab] = useState<'all' | 'blogs' | 'research'>('all');

  // Get research specific to this project
  const projectResearch = React.useMemo(() => {
    if (!projectId) return [];
    return getResearchByProject(projectId);
  }, [projectId, getResearchByProject]);

  // Combine saved research with project contents
  const allProjectContents = React.useMemo(() => {
    const researchContents: ProjectContent[] = projectResearch.map(research => ({
      id: research.id,
      title: research.topic,
      type: 'research' as const,
      status: 'completed' as const,
      word_count: 0, // Research doesn't have word count
      gpt_rank_score: research.confidence * 100,
      created_at: research.savedAt,
      updated_at: research.savedAt
    }));

    // Combine with existing project contents (blogs, etc.)
    return [...projectContents, ...researchContents];
  }, [projectContents, projectResearch]);

  // Load project data
  useEffect(() => {
    if (projectId) {
      loadProject(projectId);
      // TODO: Load project contents from API
      // loadProjectContents(projectId);
    }
  }, [projectId, loadProject]);

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects');
  };

  const handleCreateBlog = () => {
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/content-builder/${projectId}`);
  };

  const handleCreateResearch = () => {
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/research?project=${projectId}`);
  };

  const handleViewContent = (contentId: string, type: string) => {
    if (type === 'blog') {
      setLocation(`/dashboard/herramientas/seo-gpt-optimizer/content-builder/${projectId}/${contentId}`);
    } else if (type === 'research') {
      // Navigate to saved research page with the specific research highlighted
      setLocation(`/dashboard/herramientas/seo-gpt-optimizer/saved-research?highlight=${contentId}`);
    }
  };

  const filteredContents = allProjectContents.filter(content => {
    if (activeTab === 'all') return true;
    if (activeTab === 'blogs') return content.type === 'blog';
    if (activeTab === 'research') return content.type === 'research';
    return true;
  });

  // NO LOADING - Show content directly

  if (!currentProject) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Proyecto no encontrado</h2>
          <button
            onClick={handleBack}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← Volver a proyectos
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <Folder className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{currentProject.title}</h1>
                    <p className="text-gray-600 text-sm">{currentProject.topic}</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={handleCreateResearch}
                  className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-colors duration-200"
                >
                  <Search className="w-4 h-4" />
                  Nuevo Research
                </button>
                
                <button
                  onClick={handleCreateBlog}
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                >
                  <Plus className="w-4 h-4" />
                  Nuevo Blog
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Project Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Total Contenidos</p>
                  <p className="text-2xl font-bold text-gray-900">{allProjectContents.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Blogs Publicados</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {allProjectContents.filter(c => c.type === 'blog' && c.status === 'published').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Research Realizados</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {allProjectContents.filter(c => c.type === 'research').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <Search className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Puntuación Promedio</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(currentProject.current_gpt_rank_score || 0).toFixed(1)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <Target className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Content Tabs */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-200">
              <div className="flex">
                {[
                  { key: 'all', label: 'Todo', count: allProjectContents.length },
                  { key: 'blogs', label: 'Blogs', count: allProjectContents.filter(c => c.type === 'blog').length },
                  { key: 'research', label: 'Research', count: allProjectContents.filter(c => c.type === 'research').length }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as any)}
                    className={`px-6 py-4 text-sm font-medium transition-colors duration-200 ${
                      activeTab === tab.key
                        ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>
            </div>

            {/* Content List */}
            <div className="p-6">
              {filteredContents.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No hay contenido aún</h3>
                  <p className="text-gray-600 mb-6">Comienza creando tu primer blog o research para este proyecto</p>
                  <div className="flex items-center justify-center gap-3">
                    <button
                      onClick={handleCreateResearch}
                      className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-colors duration-200"
                    >
                      <Search className="w-4 h-4" />
                      Crear Research
                    </button>
                    <button
                      onClick={handleCreateBlog}
                      className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                    >
                      <Plus className="w-4 h-4" />
                      Crear Blog
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredContents.map((content, index) => (
                    <motion.div
                      key={content.id}
                      className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 cursor-pointer"
                      onClick={() => handleViewContent(content.id, content.type)}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                            content.type === 'blog' 
                              ? 'bg-blue-100 text-blue-600' 
                              : 'bg-purple-100 text-purple-600'
                          }`}>
                            {content.type === 'blog' ? <FileText className="w-5 h-5" /> : <Search className="w-5 h-5" />}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{content.title}</h3>
                            <div className="flex items-center gap-4 mt-1">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                content.status === 'published' ? 'bg-green-100 text-green-700' :
                                content.status === 'completed' ? 'bg-blue-100 text-blue-700' :
                                'bg-gray-100 text-gray-700'
                              }`}>
                                {content.type === 'research' ? 'Investigación' : content.status}
                              </span>
                              {content.type === 'blog' && (
                                <span className="text-xs text-gray-500">
                                  {content.word_count} palabras
                                </span>
                              )}
                              <span className="text-xs text-gray-500">
                                {content.type === 'research'
                                  ? `Confianza: ${content.gpt_rank_score.toFixed(0)}%`
                                  : `Score: ${content.gpt_rank_score}`
                                }
                              </span>
                              <span className="text-xs text-gray-500">
                                {new Date(content.created_at).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg transition-colors duration-200">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors duration-200">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg transition-colors duration-200">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default ProjectDashboardPage;
