/**
 * SEO & GPT Optimizer™ - Main Application Component
 * Root component adapted for Emma Studio's wouter routing system
 */

import React from 'react';
import { Switch, Route, useLocation } from 'wouter';
import { SEOGPTOptimizerProvider } from '../../context/SEOGPTOptimizerContext';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';

// Direct imports - NO LAZY LOADING
import SEOGPTOptimizerDashboard from './index';
import ResearchPage from './research-page';
import SavedResearchPage from './saved-research-page';
import ResearchManagementPage from './research-management-page';
import ContentBuilderPage from './content-builder-page';
import AnalyticsPage from './analytics-page';
import ProjectsPage from './projects-page';
import ProjectDashboardPage from './project-dashboard-page';
import LexicalEditorTest from '../../components/seo-gpt-optimizer/content-builder/LexicalEditorTest';

// Simple route wrapper with just error boundary
const RouteWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ErrorBoundary>
    {children}
  </ErrorBoundary>
);

const SEOGPTOptimizerApp: React.FC = () => {
  const [location] = useLocation();

  // Extract the sub-path after /dashboard/herramientas/seo-gpt-optimizer
  const basePath = '/dashboard/herramientas/seo-gpt-optimizer';
  const subPath = location.replace(basePath, '') || '/';

  return (
    <ErrorBoundary>
      <SEOGPTOptimizerProvider>
        <Switch>
          {/* Main Dashboard */}
          <Route path={`${basePath}`}>
            <RouteWrapper>
              <SEOGPTOptimizerDashboard />
            </RouteWrapper>
          </Route>

          {/* Research Engine */}
          <Route path={`${basePath}/research`}>
            <RouteWrapper>
              <ResearchPage />
            </RouteWrapper>
          </Route>

          {/* Saved Research */}
          <Route path={`${basePath}/saved-research`}>
            <RouteWrapper>
              <SavedResearchPage />
            </RouteWrapper>
          </Route>

          {/* Research Management */}
          <Route path={`${basePath}/research-management`}>
            <RouteWrapper>
              <ResearchManagementPage />
            </RouteWrapper>
          </Route>

          {/* Content Builder */}
          <Route path={`${basePath}/content-builder`}>
            <RouteWrapper>
              <ContentBuilderPage />
            </RouteWrapper>
          </Route>

          {/* Content Builder with Project ID */}
          <Route path={`${basePath}/content-builder/:projectId`}>
            {(params) => (
              <RouteWrapper>
                <ContentBuilderPage projectId={params.projectId} />
              </RouteWrapper>
            )}
          </Route>

          {/* Analytics Dashboard */}
          <Route path={`${basePath}/analytics`}>
            <RouteWrapper>
              <AnalyticsPage />
            </RouteWrapper>
          </Route>

          {/* Project Management */}
          <Route path={`${basePath}/projects`}>
            <RouteWrapper>
              <ProjectsPage />
            </RouteWrapper>
          </Route>

          {/* Individual Project Dashboard */}
          <Route path={`${basePath}/project/:projectId`}>
            <RouteWrapper>
              <ProjectDashboardPage />
            </RouteWrapper>
          </Route>

          {/* Lexical Editor Test */}
          <Route path={`${basePath}/lexical-test`}>
            <RouteWrapper>
              <LexicalEditorTest />
            </RouteWrapper>
          </Route>
        </Switch>
      </SEOGPTOptimizerProvider>
    </ErrorBoundary>
  );
};

export default SEOGPTOptimizerApp;
