/**
 * Performance Optimization Utilities
 * Ensures instant feedback and eliminates loading screens
 */

// Optimized debounce for real-time feedback
export function createOptimizedDebounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: {
    leading?: boolean;
    trailing?: boolean;
    maxWait?: number;
  } = {}
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  let maxTimeout: NodeJS.Timeout | null = null;
  let lastCallTime = 0;
  let lastInvokeTime = 0;
  let lastArgs: Parameters<T>;
  let result: ReturnType<T>;

  const { leading = false, trailing = true, maxWait } = options;

  function invokeFunc(time: number) {
    const args = lastArgs;
    lastInvokeTime = time;
    result = func(...args);
    return result;
  }

  function leadingEdge(time: number) {
    lastInvokeTime = time;
    timeout = setTimeout(timerExpired, wait);
    return leading ? invokeFunc(time) : result;
  }

  function remainingWait(time: number) {
    const timeSinceLastCall = time - lastCallTime;
    const timeSinceLastInvoke = time - lastInvokeTime;
    const timeWaiting = wait - timeSinceLastCall;

    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  }

  function shouldInvoke(time: number) {
    const timeSinceLastCall = time - lastCallTime;
    const timeSinceLastInvoke = time - lastInvokeTime;

    return (
      lastCallTime === 0 ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    );
  }

  function timerExpired() {
    const time = Date.now();
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }
    timeout = setTimeout(timerExpired, remainingWait(time));
  }

  function trailingEdge(time: number) {
    timeout = null;

    if (trailing && lastArgs) {
      return invokeFunc(time);
    }
    lastArgs = undefined as any;
    return result;
  }

  function cancel() {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    if (maxTimeout !== null) {
      clearTimeout(maxTimeout);
    }
    lastInvokeTime = 0;
    lastCallTime = 0;
    lastArgs = undefined as any;
    timeout = null;
    maxTimeout = null;
  }

  function flush() {
    return timeout === null ? result : trailingEdge(Date.now());
  }

  function debounced(...args: Parameters<T>) {
    const time = Date.now();
    const isInvoking = shouldInvoke(time);

    lastArgs = args;
    lastCallTime = time;

    if (isInvoking) {
      if (timeout === null) {
        return leadingEdge(lastCallTime);
      }
      if (maxWait !== undefined) {
        timeout = setTimeout(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }
    if (timeout === null) {
      timeout = setTimeout(timerExpired, wait);
    }
    return result;
  }

  debounced.cancel = cancel;
  debounced.flush = flush;
  return debounced;
}

// Throttle for immediate visual feedback
export function createThrottle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): (...args: Parameters<T>) => void {
  return createOptimizedDebounce(func, wait, {
    leading: true,
    maxWait: wait,
    ...options
  });
}

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startTiming(label: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.metrics.has(label)) {
        this.metrics.set(label, []);
      }
      
      this.metrics.get(label)!.push(duration);
      
      // Log slow operations (> 16ms for 60fps)
      if (duration > 16) {
        console.warn(`Slow operation detected: ${label} took ${duration.toFixed(2)}ms`);
      }
    };
  }

  getAverageTime(label: string): number {
    const times = this.metrics.get(label);
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getMetrics(): Record<string, { average: number; count: number; max: number }> {
    const result: Record<string, { average: number; count: number; max: number }> = {};
    
    for (const [label, times] of this.metrics.entries()) {
      result[label] = {
        average: this.getAverageTime(label),
        count: times.length,
        max: Math.max(...times)
      };
    }
    
    return result;
  }

  reset(): void {
    this.metrics.clear();
  }
}

// React hook for performance monitoring
export function usePerformanceMonitor(label: string) {
  const monitor = PerformanceMonitor.getInstance();
  
  return {
    startTiming: () => monitor.startTiming(label),
    getAverageTime: () => monitor.getAverageTime(label),
    getMetrics: () => monitor.getMetrics()
  };
}

// Optimize text input for real-time feedback
export function optimizeTextInput(
  onChange: (value: string) => void,
  options: {
    immediate?: boolean;
    debounceMs?: number;
    maxLength?: number;
  } = {}
) {
  const { immediate = true, debounceMs = 100, maxLength } = options;
  
  // Immediate feedback for UI updates
  const immediateHandler = immediate ? onChange : () => {};
  
  // Debounced handler for expensive operations
  const debouncedHandler = createOptimizedDebounce(onChange, debounceMs, {
    leading: false,
    trailing: true
  });
  
  return (value: string) => {
    // Truncate if max length specified
    const finalValue = maxLength ? value.slice(0, maxLength) : value;
    
    // Immediate UI update
    if (immediate) {
      immediateHandler(finalValue);
    }
    
    // Debounced processing
    debouncedHandler(finalValue);
  };
}

// Preload critical resources
export function preloadCriticalResources() {
  // Preload Emma brand colors as CSS custom properties
  const style = document.createElement('style');
  style.textContent = `
    :root {
      --emma-blue: #3018ef;
      --emma-pink: #dd3a5a;
      --emma-blue-light: #3018ef10;
      --emma-pink-light: #dd3a5a10;
    }
  `;
  document.head.appendChild(style);
  
  // Preload common fonts
  const fontLink = document.createElement('link');
  fontLink.rel = 'preload';
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap';
  fontLink.as = 'style';
  document.head.appendChild(fontLink);
}

// Initialize performance optimizations
export function initializePerformanceOptimizations() {
  // Preload resources
  preloadCriticalResources();
  
  // Set up performance observer for monitoring
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.duration > 50) { // Log operations > 50ms
          console.warn(`Long task detected: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure', 'navigation'] });
  }
  
  console.log('✅ Performance optimizations initialized');
}

export default {
  createOptimizedDebounce,
  createThrottle,
  PerformanceMonitor,
  usePerformanceMonitor,
  optimizeTextInput,
  initializePerformanceOptimizations
};
