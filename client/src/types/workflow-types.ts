/**
 * Tipos TypeScript para Emma Visual Workflow Builder
 * Sistema simplificado de workflows visuales
 */

// Tipos básicos de datos
export type DataType =
  | "text"
  | "image"
  | "video"
  | "number"
  | "boolean"
  | "url";

// Configuración de inputs
export interface InputConfig {
  type: DataType;
  label: string;
  default?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: string[];
  placeholder?: string;
  required?: boolean;
  tooltip?: string;
}

// Configuración de outputs
export interface OutputConfig {
  type: DataType;
  label: string;
}

// Definición de nodo Emma
export interface EmmaNodeDefinition {
  id: string;
  name: string;
  category: string;
  description: string;
  icon: string;
  color: string;
  inputs: Record<string, InputConfig>;
  outputs: Record<string, OutputConfig>;
  provider?: string; // API provider (ideogram, openai, stability, etc.)
  endpoint?: string; // API endpoint
}

// Tipos para React Flow
export interface NodePosition {
  x: number;
  y: number;
}

// Datos del nodo en el workflow
export interface EmmaNodeData {
  nodeType: string;
  definition: EmmaNodeDefinition;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  isValid: boolean;
  errors: string[];
  isExecuting?: boolean;
  isCompleted?: boolean;
}

// Nodo en React Flow
export interface EmmaWorkflowNode {
  id: string;
  type: string;
  position: NodePosition;
  data: EmmaNodeData;
  selected?: boolean;
  dragging?: boolean;
}

// Conexión entre nodos
export interface EmmaWorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
  type?: string;
  animated?: boolean;
  style?: Record<string, any>;
}

// Workflow completo
export interface EmmaWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: EmmaWorkflowNode[];
  edges: EmmaWorkflowEdge[];
  metadata: {
    version: string;
    created_at: string;
    updated_at: string;
    tags: string[];
    category: string;
  };
}

// Tipos para la API
export interface ComfyWorkflowFormat {
  [nodeId: string]: {
    class_type: string;
    inputs: Record<string, any>;
  };
}

export interface WorkflowExecutionRequest {
  workflow: ComfyWorkflowFormat;
  client_id?: string;
}

export interface WorkflowExecutionResponse {
  success: boolean;
  execution_id: string;
  message: string;
  outputs?: Record<string, any>;
  errors?: string[];
}

export interface WorkflowValidationResponse {
  success: boolean;
  valid: boolean;
  errors: string[];
}

export interface NodeCategoriesResponse {
  success: boolean;
  categories: Record<string, Array<{
    name: string;
    display_name: string;
    description: string;
  }>>;
}

export interface WorkflowTemplate {
  name: string;
  description: string;
  workflow: ComfyWorkflowFormat;
  preview_image?: string;
  tags?: string[];
}

export interface WorkflowTemplatesResponse {
  success: boolean;
  templates: Record<string, WorkflowTemplate>;
}

// Tipos para el estado del editor
export interface WorkflowEditorState {
  workflow: VisualWorkflow;
  selectedNode: string | null;
  isExecuting: boolean;
  executionId: string | null;
  lastExecution: WorkflowExecutionResponse | null;
  availableNodes: Record<string, ComfyNodeInfo>;
  nodeCategories: Record<string, Array<{
    name: string;
    display_name: string;
    description: string;
  }>>;
  templates: Record<string, WorkflowTemplate>;
  isLoading: boolean;
  error: string | null;
}

// Tipos para eventos del editor
export interface NodeAddEvent {
  nodeType: string;
  position: NodePosition;
}

export interface NodeUpdateEvent {
  nodeId: string;
  updates: Partial<WorkflowNodeData>;
}

export interface NodeDeleteEvent {
  nodeId: string;
}

export interface ConnectionCreateEvent {
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
}

export interface ConnectionDeleteEvent {
  connectionId: string;
}

// Tipos para los componentes de nodos personalizados
export interface CustomNodeProps {
  id: string;
  data: WorkflowNodeData;
  selected: boolean;
  onUpdate: (updates: Partial<WorkflowNodeData>) => void;
  onDelete: () => void;
}

export interface NodeInputProps {
  name: string;
  type: ComfyInputType;
  config: ComfyInputConfig;
  value: any;
  onChange: (value: any) => void;
  connected?: boolean;
}

export interface NodeOutputProps {
  name: string;
  type: ComfyInputType;
  index: number;
}

// Tipos para el panel de propiedades
export interface NodePropertiesProps {
  node: WorkflowNode | null;
  onUpdate: (updates: Partial<WorkflowNodeData>) => void;
  onClose: () => void;
}

// Tipos para la biblioteca de nodos
export interface NodeLibraryProps {
  categories: Record<string, Array<{
    name: string;
    display_name: string;
    description: string;
  }>>;
  onNodeAdd: (nodeType: string, position: NodePosition) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

// Tipos para el toolbar
export interface WorkflowToolbarProps {
  onSave: () => void;
  onLoad: () => void;
  onExecute: () => void;
  onClear: () => void;
  onExport: () => void;
  onImport: () => void;
  isExecuting: boolean;
  canExecute: boolean;
}

// Tipos para utilidades
export interface WorkflowConverter {
  visualToComfy: (visual: VisualWorkflow) => ComfyWorkflowFormat;
  comfyToVisual: (comfy: ComfyWorkflowFormat) => VisualWorkflow;
}

export interface WorkflowValidator {
  validateWorkflow: (workflow: VisualWorkflow) => string[];
  validateNode: (node: WorkflowNode) => string[];
  validateConnection: (connection: WorkflowConnection, nodes: WorkflowNode[]) => string[];
}

// Constantes
export const NODE_TYPES = {
  CUSTOM: 'customNode',
  INPUT: 'input',
  OUTPUT: 'output',
  DEFAULT: 'default'
} as const;

export const CONNECTION_TYPES = {
  DEFAULT: 'default',
  SMOOTH: 'smoothstep',
  STEP: 'step',
  STRAIGHT: 'straight'
} as const;

export const NODE_CATEGORIES = {
  CONDITIONING: 'conditioning',
  LATENT: 'latent',
  IMAGE: 'image',
  MODEL: 'model',
  SAMPLING: 'sampling',
  LOADERS: 'loaders',
  ADVANCED: 'advanced',
  UTILS: 'utils'
} as const;

// Colores para diferentes tipos de conexiones
export const CONNECTION_COLORS: Record<ComfyInputType, string> = {
  STRING: '#10b981',      // Verde
  INT: '#3b82f6',         // Azul
  FLOAT: '#8b5cf6',       // Púrpura
  BOOLEAN: '#f59e0b',     // Amarillo
  IMAGE: '#ef4444',       // Rojo
  LATENT: '#ec4899',      // Rosa
  CONDITIONING: '#06b6d4', // Cian
  MODEL: '#84cc16',       // Lima
  VAE: '#f97316',         // Naranja
  CLIP: '#6366f1'         // Índigo
};

// Configuración por defecto para React Flow
export const DEFAULT_REACT_FLOW_CONFIG = {
  nodeTypes: {},
  edgeTypes: {},
  defaultViewport: { x: 0, y: 0, zoom: 1 },
  minZoom: 0.1,
  maxZoom: 2,
  attributionPosition: 'bottom-left' as const,
  proOptions: { hideAttribution: true }
};
