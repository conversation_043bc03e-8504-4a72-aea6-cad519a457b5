/**
 * Shared Ad Controls Component
 * Reusable controls for all platform editors
 */

import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Wand2,
  Camera,
  Upload,
  X,
  RefreshCw,
  Zap,
  Sparkles
} from "lucide-react";
import { AdControlsProps } from "@/types/ad-creator-types";

export function AdControls({
  prompt,
  size,
  isGenerating,
  useProductImages,
  productImages,
  onPromptChange,
  onSizeChange,
  onUseProductImagesChange,
  onProductImageUpload,
  onRemoveProductImage,
  onGenerate,
  onFreeGeneration,
  productInputRef,
  config
}: AdControlsProps) {

  // Función para manejar la generación libre
  const handleFreeGeneration = async () => {
    if (onFreeGeneration) {
      // Si hay una función personalizada, usarla
      onFreeGeneration(prompt, config);
    } else {
      // Fallback: generar automáticamente con prompt inteligente
      const freePrompt = prompt.trim() || generateRandomPrompt(config.name);
      onGenerate();
    }
  };

  // Generar prompt aleatorio para generación libre
  const generateRandomPrompt = (platform: string): string => {
    const randomPrompts = {
      facebook: [
        "Anuncio viral para Facebook con producto innovador, colores llamativos, mensaje persuasivo",
        "Post promocional de Facebook con oferta especial, diseño moderno, call-to-action urgente",
        "Anuncio de Facebook lifestyle con producto integrado, ambiente aspiracional, colores trending"
      ],
      instagram: [
        "Post estético de Instagram con producto destacado, iluminación natural, composición minimalista",
        "Story de Instagram con producto lifestyle, colores vibrantes, diseño moderno y atractivo",
        "Feed post de Instagram con producto en contexto real, ambiente aspiracional, muy visual"
      ],
      linkedin: [
        "Anuncio profesional de LinkedIn B2B, ambiente corporativo, mensaje de valor empresarial",
        "Post de LinkedIn con solución empresarial, diseño sobrio, credibilidad profesional",
        "Anuncio corporativo de LinkedIn con resultados medibles, ambiente de oficina moderna"
      ],
      youtube: [
        "Thumbnail de YouTube llamativo con expresión facial expresiva, colores contrastantes, texto grande",
        "Miniatura de YouTube con producto destacado, diseño clickbait profesional, elementos gráficos",
        "Thumbnail viral de YouTube con transformación visible, colores impactantes, mensaje claro"
      ]
    };

    const platformPrompts = randomPrompts[platform as keyof typeof randomPrompts] || [
      "Anuncio profesional con producto destacado, diseño moderno, colores atractivos, mensaje claro"
    ];

    return platformPrompts[Math.floor(Math.random() * platformPrompts.length)];
  };
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.2 }}
      className="space-y-4"
    >
      {/* Creación básica */}
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wand2 className="h-5 w-5" />
            Crear Anuncio
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Prompt principal */}
          <div className="space-y-2">
            <Label htmlFor="prompt" className="text-sm font-medium">Descripción del anuncio</Label>
            <Textarea
              id="prompt"
              placeholder={`Describe tu anuncio para ${config.name}...`}
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              rows={3}
              className="resize-none text-sm"
            />
          </div>

          {/* Selector de tamaño */}
          <div className="space-y-2">
            <Label htmlFor="size" className="text-sm font-medium">Tamaño</Label>
            <select
              id="size"
              value={size}
              onChange={(e) => onSizeChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm"
            >
              {config.sizes.map((sizeOption) => (
                <option key={sizeOption} value={sizeOption}>
                  {sizeOption}
                </option>
              ))}
            </select>
          </div>

          {/* Botones de generación */}
          <div className="space-y-2">
            {/* Botón de generación normal */}
            <Button
              onClick={onGenerate}
              disabled={isGenerating || !prompt.trim()}
              className={`w-full bg-gradient-to-r ${config.color} hover:opacity-90 text-sm h-10`}
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Generando...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  Generar Anuncio
                </>
              )}
            </Button>

            {/* Botón de generación libre */}
            <Button
              onClick={handleFreeGeneration}
              disabled={isGenerating}
              variant="outline"
              className="w-full border-2 border-dashed border-[#dd3a5a] text-[#dd3a5a] hover:bg-[#dd3a5a] hover:text-white transition-all duration-300 text-sm h-10 group"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                  Generando Libre...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2 group-hover:animate-pulse" />
                  Generación Libre
                  <Sparkles className="w-4 h-4 ml-2 group-hover:animate-bounce" />
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sección de imágenes de producto */}
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Camera className="h-5 w-5" />
            Imágenes de Producto
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Usar imágenes de producto</Label>
              <Switch
                checked={useProductImages}
                onCheckedChange={onUseProductImagesChange}
              />
            </div>

            {useProductImages && (
              <>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-[#3018ef] transition-colors"
                  onClick={() => productInputRef.current?.click()}
                >
                  <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600">
                    Arrastra imágenes o haz clic para subir
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Máximo 3 imágenes • JPG, PNG
                  </p>
                </div>

                <input
                  ref={productInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => onProductImageUpload(e.target.files)}
                  className="hidden"
                />

                {productImages.length > 0 && (
                  <div className="grid grid-cols-3 gap-3">
                    {productImages.map((file, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Producto ${index + 1}`}
                          className="w-full h-20 object-cover rounded border"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveProductImage(index)}
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 hover:bg-red-600 text-white rounded-full"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
