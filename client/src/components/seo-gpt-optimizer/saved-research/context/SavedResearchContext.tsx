/**
 * SEO & GPT Optimizer™ - Saved Research Context
 * Context provider for saved research functionality
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useSavedResearch, SavedResearch } from '../../../../hooks/seo-gpt-optimizer/useSavedResearch';
import { exportResearchToPDF } from '../utils/pdfExport';

interface SavedResearchContextType {
  // State
  searchQuery: string;
  selectedResearch: SavedResearch | null;
  
  // Actions
  setSearchQuery: (query: string) => void;
  setSelectedResearch: (research: SavedResearch | null) => void;
  handleViewResearch: (research: SavedResearch) => void;
  handleDeleteResearch: (id: string) => void;
  handleExportResearch: (research: SavedResearch) => void;
  
  // From hook
  savedResearch: SavedResearch[];
  isLoading: boolean;
  deleteResearch: (id: string) => void;
  searchResearch: (query: string) => SavedResearch[];
}

const SavedResearchContext = createContext<SavedResearchContextType | undefined>(undefined);

interface SavedResearchProviderProps {
  children: ReactNode;
}

export const SavedResearchProvider: React.FC<SavedResearchProviderProps> = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedResearch, setSelectedResearch] = useState<SavedResearch | null>(null);
  
  const {
    savedResearch,
    isLoading,
    deleteResearch,
    searchResearch
  } = useSavedResearch();

  const handleViewResearch = (research: SavedResearch) => {
    setSelectedResearch(research);
  };

  const handleDeleteResearch = (id: string) => {
    deleteResearch(id);
    if (selectedResearch?.id === id) {
      setSelectedResearch(null);
    }
  };

  const handleExportResearch = (research: SavedResearch) => {
    exportResearchToPDF(research);
  };

  const value: SavedResearchContextType = {
    // State
    searchQuery,
    selectedResearch,
    
    // Actions
    setSearchQuery,
    setSelectedResearch,
    handleViewResearch,
    handleDeleteResearch,
    handleExportResearch,
    
    // From hook
    savedResearch,
    isLoading,
    deleteResearch,
    searchResearch
  };

  return (
    <SavedResearchContext.Provider value={value}>
      {children}
    </SavedResearchContext.Provider>
  );
};

export const useSavedResearchContext = () => {
  const context = useContext(SavedResearchContext);
  if (context === undefined) {
    throw new Error('useSavedResearchContext must be used within a SavedResearchProvider');
  }
  return context;
};
