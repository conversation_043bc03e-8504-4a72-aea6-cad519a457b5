/**
 * SEO & GPT Optimizer™ - GPT Reference Section
 * Component for displaying GPT reference data
 */

import React from 'react';

interface GPTReferenceSectionProps {
  data?: any;
}

const GPTReferenceSection: React.FC<GPTReferenceSectionProps> = ({ data }) => {
  if (!data) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Referencia GPT</h3>
      <div className="bg-purple-50 rounded-xl p-4">
        <div className="prose prose-sm max-w-none">
          <p className="text-gray-700 whitespace-pre-wrap">
            {data.response}
          </p>
        </div>
      </div>
    </div>
  );
};

export default GPTReferenceSection;
