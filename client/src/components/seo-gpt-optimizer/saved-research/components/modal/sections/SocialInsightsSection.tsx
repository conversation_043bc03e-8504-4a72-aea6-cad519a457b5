/**
 * SEO & GPT Optimizer™ - Social Insights Section
 * Component for displaying social insights data (Reddit & Quora)
 */

import React from 'react';

interface SocialInsightsSectionProps {
  data?: any;
}

const SocialInsightsSection: React.FC<SocialInsightsSectionProps> = ({ data }) => {
  if (!data) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-bold text-gray-900 mb-3">Insights Sociales</h3>
        <div className="bg-gray-50 rounded-xl p-4 text-center">
          <p className="text-gray-500 text-sm">No hay insights sociales disponibles</p>
        </div>
      </div>
    );
  }

  const redditInsights = data.reddit?.insights || [];
  const quoraInsights = data.quora?.insights || [];

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Insights Sociales</h3>
      <div className="space-y-4">
        {/* Reddit Insights */}
        {redditInsights.length > 0 && (
          <div className="bg-orange-50 rounded-xl p-4">
            <h4 className="font-semibold text-orange-800 mb-3 flex items-center gap-2">
              <span className="bg-orange-600 text-white text-xs px-2 py-1 rounded-full">Reddit</span>
              {data.reddit.total_results} insights
            </h4>
            <div className="space-y-3">
              {redditInsights.slice(0, 3).map((insight: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-3 border border-orange-200">
                  <h5 className="font-medium text-gray-900 text-sm mb-1">{insight.title}</h5>
                  <p className="text-gray-600 text-xs mb-2">r/{insight.subreddit}</p>
                  <p className="text-gray-700 text-sm">{insight.snippet}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quora Insights */}
        {quoraInsights.length > 0 && (
          <div className="bg-red-50 rounded-xl p-4">
            <h4 className="font-semibold text-red-800 mb-3 flex items-center gap-2">
              <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full">Quora</span>
              {data.quora.total_results} insights
            </h4>
            <div className="space-y-3">
              {quoraInsights.slice(0, 3).map((insight: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-3 border border-red-200">
                  <h5 className="font-medium text-gray-900 text-sm mb-1">{insight.question}</h5>
                  <p className="text-gray-700 text-sm">{insight.answer_preview}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {redditInsights.length === 0 && quoraInsights.length === 0 && (
          <div className="bg-gray-50 rounded-xl p-4 text-center">
            <p className="text-gray-500 text-sm">No hay insights sociales disponibles</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SocialInsightsSection;
