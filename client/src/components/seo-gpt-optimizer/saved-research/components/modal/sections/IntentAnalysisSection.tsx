/**
 * SEO & GPT Optimizer™ - Intent Analysis Section
 * Component for displaying intent analysis data
 */

import React from 'react';

interface IntentAnalysisSectionProps {
  data?: any;
}

const IntentAnalysisSection: React.FC<IntentAnalysisSectionProps> = ({ data }) => {
  if (!data) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-bold text-gray-900 mb-3">Análisis de Intención</h3>
        <div className="bg-gray-50 rounded-xl p-4 text-center">
          <p className="text-gray-500 text-sm">No hay datos de análisis de intención disponibles</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Análisis de Intención</h3>
      <div className="bg-gray-50 rounded-xl p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Tipo de Intención</h4>
            <p className="text-gray-600">
              {data.intent_type || 'No disponible'}
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Audiencia Objetivo</h4>
            <p className="text-gray-600">
              {data.target_audience || 'No disponible'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntentAnalysisSection;
