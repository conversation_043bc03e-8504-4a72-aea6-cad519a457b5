/**
 * SEO & GPT Optimizer™ - Frequent Questions Section
 * Component for displaying frequent questions data
 */

import React from 'react';

interface FrequentQuestionsSectionProps {
  data?: any;
}

const FrequentQuestionsSection: React.FC<FrequentQuestionsSectionProps> = ({ data }) => {
  const questions = data?.common_questions || [];

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Preguntas Frecuentes</h3>
      {questions.length > 0 ? (
        <div className="bg-red-50 rounded-xl p-4">
          <ul className="space-y-2">
            {questions.slice(0, 8).map((question: string, index: number) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-red-600 font-bold">•</span>
                <span className="text-gray-700">{question}</span>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div className="bg-gray-50 rounded-xl p-4 text-center">
          <p className="text-gray-500 text-sm">No hay preguntas frecuentes disponibles</p>
        </div>
      )}
    </div>
  );
};

export default FrequentQuestionsSection;
