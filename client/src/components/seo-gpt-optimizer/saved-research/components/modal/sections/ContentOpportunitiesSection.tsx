/**
 * SEO & GPT Optimizer™ - Content Opportunities Section
 * Component for displaying content opportunities data
 */

import React from 'react';

interface ContentOpportunitiesSectionProps {
  data?: any;
}

const ContentOpportunitiesSection: React.FC<ContentOpportunitiesSectionProps> = ({ data }) => {
  if (!data) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-bold text-gray-900 mb-3">Oportunidades de Contenido</h3>
        <div className="bg-gray-50 rounded-xl p-4 text-center">
          <p className="text-gray-500 text-sm">No hay datos de oportunidades de contenido disponibles</p>
        </div>
      </div>
    );
  }

  const contentGaps = data.content_gaps || [];
  const targetKeywords = data.target_keywords || [];

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Oportunidades de Contenido</h3>
      <div className="bg-green-50 rounded-xl p-4">
        {contentGaps.length > 0 ? (
          <div className="mb-4">
            <h4 className="font-semibold text-gray-700 mb-2">Gaps de Contenido</h4>
            <ul className="space-y-1">
              {contentGaps.slice(0, 5).map((gap: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-green-600 font-bold">•</span>
                  <span className="text-gray-700 text-sm">{gap}</span>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="mb-4">
            <h4 className="font-semibold text-gray-700 mb-2">Gaps de Contenido</h4>
            <p className="text-gray-500 text-sm">No se identificaron gaps específicos</p>
          </div>
        )}

        {targetKeywords.length > 0 ? (
          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Palabras Clave Objetivo</h4>
            <div className="flex flex-wrap gap-2">
              {targetKeywords.slice(0, 10).map((keyword: string, index: number) => (
                <span key={index} className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                  {keyword}
                </span>
              ))}
            </div>
          </div>
        ) : (
          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Palabras Clave Objetivo</h4>
            <p className="text-gray-500 text-sm">No hay palabras clave disponibles</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentOpportunitiesSection;
