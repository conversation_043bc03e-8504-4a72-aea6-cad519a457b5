/**
 * SEO & GPT Optimizer™ - Research Summary Section
 * Component for displaying research summary data
 */

import React from 'react';

interface ResearchSummarySectionProps {
  data?: any;
}

const ResearchSummarySection: React.FC<ResearchSummarySectionProps> = ({ data }) => {
  if (!data) {
    return null;
  }

  const keyFindings = data.key_findings || [];
  const recommendations = data.recommendations || [];

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Resumen de Investigación</h3>
      <div className="bg-green-50 rounded-xl p-4">
        <div className="space-y-3">
          {keyFindings.length > 0 && (
            <div>
              <h4 className="font-semibold text-green-800 mb-2">Hallazgos Clave</h4>
              <ul className="list-disc list-inside space-y-1">
                {keyFindings.map((finding: string, index: number) => (
                  <li key={index} className="text-gray-700 text-sm">{finding}</li>
                ))}
              </ul>
            </div>
          )}
          
          {recommendations.length > 0 && (
            <div>
              <h4 className="font-semibold text-green-800 mb-2">Recomendaciones</h4>
              <ul className="list-disc list-inside space-y-1">
                {recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-gray-700 text-sm">{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResearchSummarySection;
