/**
 * SEO & GPT Optimizer™ - Research Data Completeness
 * Component for displaying data completeness status
 */

import React from 'react';

interface DataCompletenessResult {
  sections: {
    intent_analysis: boolean;
    google_results: boolean;
    common_questions: boolean;
    content_opportunities: boolean;
    social_insights: boolean;
    gpt_reference: boolean;
    research_summary: boolean;
    research_quality_metrics: boolean;
  };
  completeness: number;
  isComplete: boolean;
}

interface ResearchDataCompletenessProps {
  dataCheck: DataCompletenessResult;
}

const ResearchDataCompleteness: React.FC<ResearchDataCompletenessProps> = ({
  dataCheck
}) => {
  return (
    <div className="mb-6">
      <div className={`p-4 rounded-xl ${dataCheck.isComplete ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
        <div className="flex items-center gap-2 mb-2">
          <span className={`text-sm font-medium ${dataCheck.isComplete ? 'text-green-700' : 'text-yellow-700'}`}>
            {dataCheck.isComplete ? '✅ Datos completos' : '⚠️ Datos parciales'}
          </span>
          <span className={`text-xs px-2 py-1 rounded-full ${dataCheck.isComplete ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'}`}>
            {dataCheck.completeness.toFixed(0)}% completo
          </span>
        </div>
        {!dataCheck.isComplete && (
          <p className="text-xs text-yellow-600">
            Esta investigación puede tener datos limitados. Algunas secciones podrían no estar disponibles.
          </p>
        )}
      </div>
    </div>
  );
};

export default ResearchDataCompleteness;
