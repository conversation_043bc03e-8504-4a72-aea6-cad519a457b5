/**
 * SEO & GPT Optimizer™ - Research Metrics
 * Component for displaying research metrics
 */

import React from 'react';
import { SavedResearch } from '../../../../../hooks/seo-gpt-optimizer/useSavedResearch';

interface ResearchMetricsProps {
  research: SavedResearch;
}

const ResearchMetrics: React.FC<ResearchMetricsProps> = ({ research }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div className="bg-blue-50 rounded-xl p-4">
        <div className="text-2xl font-bold text-blue-600">
          {(research.confidence * 100).toFixed(1)}%
        </div>
        <div className="text-sm text-blue-700">Confianza</div>
      </div>
      <div className="bg-green-50 rounded-xl p-4">
        <div className="text-2xl font-bold text-green-600">
          {research.processingTime.toFixed(1)}s
        </div>
        <div className="text-sm text-green-700">Tiempo de Procesamiento</div>
      </div>
      <div className="bg-purple-50 rounded-xl p-4">
        <div className="text-2xl font-bold text-purple-600">
          {(research.results as any)?.google_results?.total_results || 0}
        </div>
        <div className="text-sm text-purple-700">Resultados Analizados</div>
      </div>
    </div>
  );
};

export default ResearchMetrics;
