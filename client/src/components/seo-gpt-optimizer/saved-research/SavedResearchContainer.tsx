/**
 * SEO & GPT Optimizer™ - Saved Research Container
 * Main container component for saved research functionality
 */

import React from 'react';
import { useLocation } from 'wouter';
import { ArrowLeft, Settings } from 'lucide-react';

import { useSavedResearchContext } from './context/SavedResearchContext';
import SavedResearchHeader from './components/SavedResearchHeader';
import SavedResearchSearch from './components/SavedResearchSearch';
import SavedResearchGrid from './components/SavedResearchGrid';
import SavedResearchModal from './components/SavedResearchModal';
import SavedResearchEmptyState from './components/SavedResearchEmptyState';

const SavedResearchContainer: React.FC = () => {
  const [, setLocation] = useLocation();
  const {
    savedResearch,
    isLoading,
    searchQuery,
    selectedResearch,
    searchResearch
  } = useSavedResearchContext();

  const handleBack = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleManageResearch = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/research-management');
  };

  const filteredResearch = searchQuery.trim()
    ? searchResearch(searchQuery)
    : savedResearch;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* Header */}
      <SavedResearchHeader 
        onBack={handleBack}
        onManage={handleManageResearch}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar */}
        <SavedResearchSearch />

        {/* Content */}
        {isLoading ? (
          <SavedResearchEmptyState 
            type="loading"
            title="Cargando investigaciones..."
          />
        ) : filteredResearch.length === 0 ? (
          <SavedResearchEmptyState 
            type={searchQuery ? 'no-results' : 'empty'}
            title={searchQuery ? 'No se encontraron investigaciones' : 'No hay investigaciones guardadas'}
            description={searchQuery ? 'Intenta con otros términos de búsqueda' : 'Guarda investigaciones desde el Research Engine para verlas aquí'}
          />
        ) : (
          <SavedResearchGrid research={filteredResearch} />
        )}

        {/* Modal */}
        {selectedResearch && (
          <SavedResearchModal research={selectedResearch} />
        )}
      </div>
    </div>
  );
};

export default SavedResearchContainer;
