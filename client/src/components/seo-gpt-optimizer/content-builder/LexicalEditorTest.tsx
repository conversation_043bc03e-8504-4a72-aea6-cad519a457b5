/**
 * Advanced Formatting Test Component
 * Showcases all advanced formatting features including colors, fonts, and styles
 */

import React, { useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { HeadingNode } from '@lexical/rich-text';
import { ListNode, ListItemNode } from '@lexical/list';
import { LinkNode } from '@lexical/link';
import { $getRoot } from 'lexical';
import AdvancedFormattingToolbar from './AdvancedFormattingToolbar';
import './AdvancedFormattingToolbar.css';
import './LexicalEditor.css';

const LexicalEditorTest: React.FC = () => {
  const [content, setContent] = useState('');

  const initialConfig = {
    namespace: 'AdvancedFormattingTest',
    theme: {
      text: {
        bold: 'PlaygroundEditorTheme__bold',
        italic: 'PlaygroundEditorTheme__italic',
        underline: 'PlaygroundEditorTheme__underline',
        strikethrough: 'PlaygroundEditorTheme__strikethrough',
        underlineStrikethrough: 'PlaygroundEditorTheme__underlineStrikethrough',
        subscript: 'PlaygroundEditorTheme__subscript',
        superscript: 'PlaygroundEditorTheme__superscript',
        code: 'PlaygroundEditorTheme__textCode',
      },
      paragraph: 'PlaygroundEditorTheme__paragraph',
      heading: {
        h1: 'PlaygroundEditorTheme__h1',
        h2: 'PlaygroundEditorTheme__h2',
        h3: 'PlaygroundEditorTheme__h3',
        h4: 'PlaygroundEditorTheme__h4',
        h5: 'PlaygroundEditorTheme__h5',
        h6: 'PlaygroundEditorTheme__h6',
      },
      list: {
        nested: {
          listitem: 'PlaygroundEditorTheme__listItem',
        },
        ol: 'PlaygroundEditorTheme__ol',
        ul: 'PlaygroundEditorTheme__ul',
        listitem: 'PlaygroundEditorTheme__listItem',
        listitemChecked: 'PlaygroundEditorTheme__listItemChecked',
        listitemUnchecked: 'PlaygroundEditorTheme__listItemUnchecked',
      },
      link: 'PlaygroundEditorTheme__link',
      code: 'PlaygroundEditorTheme__code',
      codeBlock: 'PlaygroundEditorTheme__codeBlock',
      mark: 'PlaygroundEditorTheme__mark',
      markOverlap: 'PlaygroundEditorTheme__markOverlap',
      quote: 'PlaygroundEditorTheme__quote',
      hashtag: 'PlaygroundEditorTheme__hashtag',
    },
    nodes: [HeadingNode, ListNode, ListItemNode, LinkNode],
    onError: (error: Error) => {
      console.error('Lexical error:', error);
    },
  };

  const handleChange = (editorState: any) => {
    editorState.read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();
      setContent(textContent);
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Advanced Formatting Test</h1>
        <p className="text-gray-600">Test all advanced formatting features including colors, fonts, and styles</p>
      </div>

      {/* Editor Container */}
      <div className="max-w-6xl mx-auto p-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Advanced Formatting Toolbar */}
          <LexicalComposer initialConfig={initialConfig}>
            <AdvancedFormattingToolbar />

            {/* Editor Area */}
            <div className="p-8">
              <RichTextPlugin
                contentEditable={
                  <ContentEditable
                    className="lexical-editor min-h-[400px] outline-none p-6 border border-gray-200 rounded-lg bg-white"
                    style={{
                      fontFamily: 'Arial, sans-serif',
                      fontSize: '11pt',
                      lineHeight: '1.5',
                      color: '#202124'
                    }}
                    aria-placeholder="Start typing to test advanced formatting features..."
                    placeholder={
                      <div className="text-gray-400 absolute top-6 left-6 pointer-events-none">
                        Start typing to test advanced formatting features...
                      </div>
                    }
                  />
                }
                placeholder={
                  <div className="text-gray-400 absolute top-6 left-6 pointer-events-none">
                    Start typing to test advanced formatting features...
                  </div>
                }
                ErrorBoundary={LexicalErrorBoundary}
              />
              <HistoryPlugin />
              <ListPlugin />
              <LinkPlugin />
              <OnChangePlugin onChange={handleChange} />
            </div>
          </LexicalComposer>
        </div>

        {/* Feature Guide */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Keyboard Shortcuts */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">Keyboard Shortcuts</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Bold</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+B</kbd>
              </div>
              <div className="flex justify-between">
                <span>Italic</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+I</kbd>
              </div>
              <div className="flex justify-between">
                <span>Underline</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+U</kbd>
              </div>
              <div className="flex justify-between">
                <span>Undo</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+Z</kbd>
              </div>
              <div className="flex justify-between">
                <span>Redo</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+Shift+Z</kbd>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">Advanced Features</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>✅ Font Family Selection (10 fonts)</li>
              <li>✅ Font Size Control (8pt - 72pt)</li>
              <li>✅ Text Colors (40+ colors)</li>
              <li>✅ Background Colors</li>
              <li>✅ Bold, Italic, Underline</li>
              <li>✅ Strikethrough</li>
              <li>✅ Text Alignment</li>
              <li>✅ Bullet & Numbered Lists</li>
              <li>✅ Undo/Redo System</li>
              <li>✅ Professional Styling</li>
            </ul>
          </div>
        </div>

        {/* Content Output */}
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">Live Content Output</h3>
          <div className="bg-gray-50 p-4 rounded border">
            <pre className="text-sm text-gray-700 whitespace-pre-wrap">
              {content || 'No content yet... Start typing to see the output!'}
            </pre>
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Character count: {content.length} | Word count: {content.split(/\s+/).filter(w => w.length > 0).length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LexicalEditorTest;
