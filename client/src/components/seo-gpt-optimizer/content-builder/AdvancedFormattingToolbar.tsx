/**
 * Advanced Formatting Toolbar for Lexical Editor
 * Professional Google Docs-style toolbar with colors, fonts, and styles
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  Bold, Italic, Underline, Strikethrough, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Type, Palette, ChevronDown, Undo, Redo,
  Subscript, Superscript, Code, Highlighter, MoreHorizontal
} from 'lucide-react';
import {
  FORMAT_TEXT_COMMAND,
  FORMAT_ELEMENT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND,
  $getSelection,
  $isRangeSelection,
  TextFormatType,
  ElementFormatType,
  $createTextNode,
  $isTextNode,
} from 'lexical';
import {
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
} from '@lexical/list';
import { $setBlocksType } from '@lexical/selection';
import { $createHeadingNode, HeadingTagType } from '@lexical/rich-text';

interface AdvancedFormattingToolbarProps {
  className?: string;
}

const AdvancedFormattingToolbar: React.FC<AdvancedFormattingToolbarProps> = ({
  className = ''
}) => {
  const [editor] = useLexicalComposerContext();
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showFontPicker, setShowFontPicker] = useState(false);
  const [showSizePicker, setShowSizePicker] = useState(false);
  const [currentFont, setCurrentFont] = useState('Arial');
  const [currentSize, setCurrentSize] = useState('11');
  const [currentColor, setCurrentColor] = useState('#000000');
  const [currentBgColor, setCurrentBgColor] = useState('#ffffff');

  const colorPickerRef = useRef<HTMLDivElement>(null);
  const fontPickerRef = useRef<HTMLDivElement>(null);
  const sizePickerRef = useRef<HTMLDivElement>(null);

  // Font options
  const fonts = [
    'Arial', 'Calibri', 'Times New Roman', 'Georgia', 'Verdana',
    'Helvetica', 'Comic Sans MS', 'Impact', 'Trebuchet MS', 'Courier New'
  ];

  // Font sizes
  const sizes = ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '28', '32', '36', '48', '72'];

  // Color palette
  const colors = [
    '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef', '#f3f3f3', '#ffffff',
    '#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#4a86e8', '#0000ff', '#9900ff', '#ff00ff',
    '#e6b8af', '#f4cccc', '#fce5cd', '#fff2cc', '#d9ead3', '#d0e0e3', '#c9daf8', '#cfe2f3', '#d9d2e9', '#ead1dc',
    '#dd7e6b', '#ea9999', '#f9cb9c', '#ffe599', '#b6d7a8', '#a2c4c9', '#a4c2f4', '#9fc5e8', '#b4a7d6', '#d5a6bd',
    '#cc4125', '#e06666', '#f6b26b', '#ffd966', '#93c47d', '#76a5af', '#6d9eeb', '#6fa8dc', '#8e7cc3', '#c27ba0',
    '#a61c00', '#cc0000', '#e69138', '#f1c232', '#6aa84f', '#45818e', '#3c78d8', '#3d85c6', '#674ea7', '#a64d79',
    '#85200c', '#990000', '#b45f06', '#bf9000', '#38761d', '#134f5c', '#1155cc', '#0b5394', '#351c75', '#741b47',
    '#5b0f00', '#660000', '#783f04', '#7f6000', '#274e13', '#0c343d', '#1c4587', '#073763', '#20124d', '#4c1130'
  ];

  // Update active formats based on selection
  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const formats = new Set<string>();
      
      if (selection.hasFormat('bold')) formats.add('bold');
      if (selection.hasFormat('italic')) formats.add('italic');
      if (selection.hasFormat('underline')) formats.add('underline');
      if (selection.hasFormat('strikethrough')) formats.add('strikethrough');
      if (selection.hasFormat('subscript')) formats.add('subscript');
      if (selection.hasFormat('superscript')) formats.add('superscript');
      if (selection.hasFormat('code')) formats.add('code');
      
      setActiveFormats(formats);
    }
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateToolbar();
      });
    });
  }, [editor, updateToolbar]);

  // Format text commands
  const formatText = (format: TextFormatType) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  };

  const formatElement = (format: ElementFormatType) => {
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
  };

  const insertList = (listType: 'bullet' | 'number') => {
    if (listType === 'bullet') {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    }
  };

  const setHeading = (headingType: HeadingTagType | 'paragraph') => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        if (headingType === 'paragraph') {
          $setBlocksType(selection, () => $createHeadingNode('p' as HeadingTagType));
        } else {
          $setBlocksType(selection, () => $createHeadingNode(headingType));
        }
      }
    });
  };

  const applyTextColor = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // Get all text nodes in selection
        const textNodes = selection.getNodes().filter($isTextNode);

        if (textNodes.length === 0) {
          // If no text selected, apply to the whole selection
          const anchor = selection.anchor;
          const focus = selection.focus;

          // Create a new text node with the color style
          const textContent = selection.getTextContent();
          if (textContent) {
            const newTextNode = $createTextNode(textContent);
            newTextNode.setStyle(`color: ${color}`);
            selection.insertNodes([newTextNode]);
          }
        } else {
          // Apply color to existing text nodes
          textNodes.forEach((node) => {
            const currentStyle = node.getStyle() || '';
            const newStyle = currentStyle.replace(/color:[^;]*;?/g, '') + `color: ${color};`;
            node.setStyle(newStyle.replace(/;;/g, ';').trim());
          });
        }
      }
    });
    setCurrentColor(color);
    setShowColorPicker(false);
  };

  const applyBackgroundColor = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const textNodes = selection.getNodes().filter($isTextNode);

        if (textNodes.length === 0) {
          const textContent = selection.getTextContent();
          if (textContent) {
            const newTextNode = $createTextNode(textContent);
            newTextNode.setStyle(`background-color: ${color}`);
            selection.insertNodes([newTextNode]);
          }
        } else {
          textNodes.forEach((node) => {
            const currentStyle = node.getStyle() || '';
            const newStyle = currentStyle.replace(/background-color:[^;]*;?/g, '') + `background-color: ${color};`;
            node.setStyle(newStyle.replace(/;;/g, ';').trim());
          });
        }
      }
    });
    setCurrentBgColor(color);
  };

  const applyFont = (font: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const textNodes = selection.getNodes().filter($isTextNode);

        if (textNodes.length === 0) {
          const textContent = selection.getTextContent();
          if (textContent) {
            const newTextNode = $createTextNode(textContent);
            newTextNode.setStyle(`font-family: "${font}"`);
            selection.insertNodes([newTextNode]);
          }
        } else {
          textNodes.forEach((node) => {
            const currentStyle = node.getStyle() || '';
            const newStyle = currentStyle.replace(/font-family:[^;]*;?/g, '') + `font-family: "${font}";`;
            node.setStyle(newStyle.replace(/;;/g, ';').trim());
          });
        }
      }
    });
    setCurrentFont(font);
    setShowFontPicker(false);
  };

  const applyFontSize = (size: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const textNodes = selection.getNodes().filter($isTextNode);

        if (textNodes.length === 0) {
          const textContent = selection.getTextContent();
          if (textContent) {
            const newTextNode = $createTextNode(textContent);
            newTextNode.setStyle(`font-size: ${size}pt`);
            selection.insertNodes([newTextNode]);
          }
        } else {
          textNodes.forEach((node) => {
            const currentStyle = node.getStyle() || '';
            const newStyle = currentStyle.replace(/font-size:[^;]*;?/g, '') + `font-size: ${size}pt;`;
            node.setStyle(newStyle.replace(/;;/g, ';').trim());
          });
        }
      }
    });
    setCurrentSize(size);
    setShowSizePicker(false);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {
        setShowColorPicker(false);
      }
      if (fontPickerRef.current && !fontPickerRef.current.contains(event.target as Node)) {
        setShowFontPicker(false);
      }
      if (sizePickerRef.current && !sizePickerRef.current.contains(event.target as Node)) {
        setShowSizePicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const ToolbarButton: React.FC<{
    onClick: () => void;
    isActive?: boolean;
    title: string;
    children: React.ReactNode;
  }> = ({ onClick, isActive = false, title, children }) => (
    <button
      onClick={onClick}
      className={`toolbar-btn ${isActive ? 'active' : ''}`}
      title={title}
    >
      {children}
    </button>
  );

  return (
    <div className={`advanced-formatting-toolbar ${className}`}>
      {/* Undo/Redo */}
      <div className="toolbar-group">
        <ToolbarButton
          onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
          title="Deshacer (Ctrl+Z)"
        >
          <Undo size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
          title="Rehacer (Ctrl+Shift+Z)"
        >
          <Redo size={16} />
        </ToolbarButton>
      </div>

      <div className="toolbar-separator" />

      {/* Font Family */}
      <div className="toolbar-group" ref={fontPickerRef}>
        <button
          className="toolbar-dropdown"
          onClick={() => setShowFontPicker(!showFontPicker)}
          title="Fuente"
        >
          <span className="dropdown-text">{currentFont}</span>
          <ChevronDown size={14} />
        </button>
        {showFontPicker && (
          <div className="dropdown-menu font-dropdown">
            {fonts.map((font) => (
              <button
                key={font}
                className={`dropdown-item ${currentFont === font ? 'active' : ''}`}
                onClick={() => applyFont(font)}
                style={{ fontFamily: font }}
              >
                {font}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Font Size */}
      <div className="toolbar-group" ref={sizePickerRef}>
        <button
          className="toolbar-dropdown size-dropdown"
          onClick={() => setShowSizePicker(!showSizePicker)}
          title="Tamaño"
        >
          <span className="dropdown-text">{currentSize}</span>
          <ChevronDown size={14} />
        </button>
        {showSizePicker && (
          <div className="dropdown-menu size-dropdown-menu">
            {sizes.map((size) => (
              <button
                key={size}
                className={`dropdown-item ${currentSize === size ? 'active' : ''}`}
                onClick={() => applyFontSize(size)}
              >
                {size}
              </button>
            ))}
          </div>
        )}
      </div>

      <div className="toolbar-separator" />

      {/* Text Formatting */}
      <div className="toolbar-group">
        <ToolbarButton
          onClick={() => formatText('bold')}
          isActive={activeFormats.has('bold')}
          title="Negrita (Ctrl+B)"
        >
          <Bold size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('italic')}
          isActive={activeFormats.has('italic')}
          title="Cursiva (Ctrl+I)"
        >
          <Italic size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('underline')}
          isActive={activeFormats.has('underline')}
          title="Subrayado (Ctrl+U)"
        >
          <Underline size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatText('strikethrough')}
          isActive={activeFormats.has('strikethrough')}
          title="Tachado"
        >
          <Strikethrough size={16} />
        </ToolbarButton>
      </div>

      <div className="toolbar-separator" />

      {/* Colors */}
      <div className="toolbar-group" ref={colorPickerRef}>
        <button
          className="toolbar-btn color-btn"
          onClick={() => setShowColorPicker(!showColorPicker)}
          title="Color de texto"
        >
          <Type size={16} />
          <div className="color-indicator" style={{ backgroundColor: currentColor }} />
        </button>
        {showColorPicker && (
          <div className="dropdown-menu color-picker">
            <div className="color-section">
              <h4>Color de texto</h4>
              <div className="color-grid">
                {colors.map((color) => (
                  <button
                    key={color}
                    className="color-swatch"
                    style={{ backgroundColor: color }}
                    onClick={() => applyTextColor(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>
            <div className="color-section">
              <h4>Color de fondo</h4>
              <div className="color-grid">
                {colors.map((color) => (
                  <button
                    key={`bg-${color}`}
                    className="color-swatch"
                    style={{ backgroundColor: color }}
                    onClick={() => applyBackgroundColor(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="toolbar-separator" />

      {/* Alignment */}
      <div className="toolbar-group">
        <ToolbarButton
          onClick={() => formatElement('left')}
          title="Alinear izquierda"
        >
          <AlignLeft size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatElement('center')}
          title="Centrar"
        >
          <AlignCenter size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => formatElement('right')}
          title="Alinear derecha"
        >
          <AlignRight size={16} />
        </ToolbarButton>
      </div>

      <div className="toolbar-separator" />

      {/* Lists */}
      <div className="toolbar-group">
        <ToolbarButton
          onClick={() => insertList('bullet')}
          title="Lista con viñetas"
        >
          <List size={16} />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => insertList('number')}
          title="Lista numerada"
        >
          <ListOrdered size={16} />
        </ToolbarButton>
      </div>
    </div>
  );
};

export default AdvancedFormattingToolbar;
