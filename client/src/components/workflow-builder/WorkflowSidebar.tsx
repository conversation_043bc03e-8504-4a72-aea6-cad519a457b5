/**
 * WorkflowSidebar - Sidebar colapsable con nodos disponibles
 * Componente independiente para el AI Workflow Builder
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Search, 
  ChevronDown, 
  ChevronRight, 
  X,
  Layers
} from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { EMMA_NODES, NODE_CATEGORIES } from '@/data/emma-nodes';

interface WorkflowSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  onNodeAdd: (nodeType: string) => void;
}

export const WorkflowSidebar: React.FC<WorkflowSidebarProps> = ({
  isOpen,
  onToggle,
  onNodeAdd
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['Entrada', 'Generación'])
  );

  // Filtrar nodos basado en búsqueda
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return NODE_CATEGORIES;

    const filtered: typeof NODE_CATEGORIES = {};
    const query = searchQuery.toLowerCase();

    Object.entries(NODE_CATEGORIES).forEach(([categoryName, nodeIds]) => {
      const filteredNodes = nodeIds.filter(nodeId => {
        const node = EMMA_NODES[nodeId];
        return node.name.toLowerCase().includes(query) ||
               node.description.toLowerCase().includes(query) ||
               node.category.toLowerCase().includes(query);
      });

      if (filteredNodes.length > 0) {
        filtered[categoryName] = filteredNodes;
      }
    });

    return filtered;
  }, [searchQuery]);

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const handleNodeDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  if (!isOpen) {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="w-12 bg-black/20 backdrop-blur-sm border-r border-purple-500/20 flex flex-col items-center py-4"
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggle}
          className="text-purple-300 hover:bg-purple-500/20 p-2"
        >
          <Plus className="h-5 w-5" />
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="w-80 bg-black/20 backdrop-blur-sm border-r border-purple-500/20 flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-purple-500/20">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Layers className="h-5 w-5 mr-2 text-purple-400" />
            Nodos Emma
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="text-purple-300 hover:bg-purple-500/20 p-1"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
          <Input
            placeholder="Buscar nodos..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
          />
        </div>
      </div>

      {/* Categories - Scrollable */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        <AnimatePresence>
          {Object.entries(filteredCategories).map(([categoryName, nodeIds]) => (
            <motion.div
              key={categoryName}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-black/20 border border-purple-500/20 rounded-lg"
            >
              {/* Category Header */}
              <div 
                className="p-3 cursor-pointer hover:bg-purple-500/10 transition-colors flex items-center justify-between"
                onClick={() => toggleCategory(categoryName)}
              >
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-white">{categoryName}</span>
                  <Badge variant="outline" className="border-purple-400 text-purple-300 text-xs">
                    {nodeIds.length}
                  </Badge>
                </div>
                {expandedCategories.has(categoryName) ? (
                  <ChevronDown className="h-4 w-4 text-purple-400" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-purple-400" />
                )}
              </div>

              {/* Category Content */}
              <AnimatePresence>
                {expandedCategories.has(categoryName) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="px-3 pb-3 space-y-2"
                  >
                    {nodeIds.map(nodeId => {
                      const node = EMMA_NODES[nodeId];
                      return (
                        <motion.button
                          key={nodeId}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => onNodeAdd(nodeId)}
                          draggable
                          onDragStart={(e) => handleNodeDragStart(e, nodeId)}
                          className="w-full p-3 bg-purple-500/10 hover:bg-purple-500/20 border border-purple-500/30 rounded-lg text-left transition-colors"
                        >
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{node.icon}</span>
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium text-white truncate">
                                {node.name}
                              </div>
                              <div className="text-xs text-purple-200 truncate">
                                {node.description}
                              </div>
                            </div>
                          </div>
                        </motion.button>
                      );
                    })}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Empty State */}
        {Object.keys(filteredCategories).length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8"
          >
            <Search className="h-12 w-12 text-purple-400 mx-auto mb-3" />
            <p className="text-purple-200">No se encontraron nodos</p>
            <p className="text-sm text-purple-300 mt-1">
              Intenta con otros términos de búsqueda
            </p>
          </motion.div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-purple-500/20">
        <p className="text-xs text-purple-300 text-center">
          Arrastra o haz clic para agregar nodos
        </p>
      </div>
    </motion.div>
  );
};
