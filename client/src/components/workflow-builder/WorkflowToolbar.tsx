/**
 * Toolbar del Workflow Builder - Controles principales
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Save, 
  Upload, 
  Download, 
  Trash2, 
  FolderOpen,
  Loader2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { WorkflowToolbarProps } from '@/types/workflow-types';

export const WorkflowToolbar: React.FC<WorkflowToolbarProps> = ({
  onSave,
  onLoad,
  onExecute,
  onClear,
  onExport,
  onImport,
  isExecuting,
  canExecute
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="flex items-center space-x-2"
    >
      {/* Execute Button */}
      <Button
        onClick={onExecute}
        disabled={!canExecute || isExecuting}
        className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium"
      >
        {isExecuting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Ejecutando...
          </>
        ) : (
          <>
            <Play className="h-4 w-4 mr-2" />
            Ejecutar
          </>
        )}
      </Button>

      {/* Separator */}
      <div className="w-px h-6 bg-purple-500/30" />

      {/* Save Button */}
      <Button
        variant="outline"
        onClick={onSave}
        className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
      >
        <Save className="h-4 w-4 mr-2" />
        Guardar
      </Button>

      {/* Load Button */}
      <Button
        variant="outline"
        onClick={onLoad}
        className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
      >
        <FolderOpen className="h-4 w-4 mr-2" />
        Cargar
      </Button>

      {/* Separator */}
      <div className="w-px h-6 bg-purple-500/30" />

      {/* Export Button */}
      <Button
        variant="outline"
        onClick={onExport}
        className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
      >
        <Download className="h-4 w-4 mr-2" />
        Exportar
      </Button>

      {/* Import Button */}
      <Button
        variant="outline"
        onClick={onImport}
        className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
      >
        <Upload className="h-4 w-4 mr-2" />
        Importar
      </Button>

      {/* Separator */}
      <div className="w-px h-6 bg-purple-500/30" />

      {/* Clear Button */}
      <Button
        variant="outline"
        onClick={onClear}
        className="border-red-500/30 text-red-300 hover:bg-red-500/10"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Limpiar
      </Button>
    </motion.div>
  );
};
