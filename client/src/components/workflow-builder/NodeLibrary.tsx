/**
 * Biblioteca de Nodos - Panel lateral con nodos disponibles
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  ChevronDown, 
  ChevronRight, 
  Layers,
  Zap,
  Image,
  Settings,
  Cpu
} from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { NodeLibraryProps, NodePosition } from '@/types/workflow-types';

// Iconos para categorías
const categoryIcons: Record<string, React.ReactNode> = {
  'conditioning': <Zap className="h-4 w-4" />,
  'latent': <Layers className="h-4 w-4" />,
  'image': <Image className="h-4 w-4" />,
  'model': <Cpu className="h-4 w-4" />,
  'sampling': <Settings className="h-4 w-4" />,
  'loaders': <Settings className="h-4 w-4" />,
  'advanced': <Settings className="h-4 w-4" />,
  'utils': <Settings className="h-4 w-4" />
};

export const NodeLibrary: React.FC<NodeLibraryProps> = ({
  categories,
  onNodeAdd,
  searchQuery,
  onSearchChange
}) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['conditioning', 'image', 'latent'])
  );

  // Filtrar nodos basado en búsqueda
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return categories;

    const filtered: typeof categories = {};
    const query = searchQuery.toLowerCase();

    Object.entries(categories).forEach(([categoryName, nodes]) => {
      const filteredNodes = nodes.filter(node =>
        node.display_name.toLowerCase().includes(query) ||
        node.description.toLowerCase().includes(query) ||
        node.name.toLowerCase().includes(query)
      );

      if (filteredNodes.length > 0) {
        filtered[categoryName] = filteredNodes;
      }
    });

    return filtered;
  }, [categories, searchQuery]);

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const handleNodeDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleNodeClick = (nodeType: string) => {
    // Agregar nodo en posición aleatoria en el centro del canvas
    const position: NodePosition = {
      x: Math.random() * 200 + 100,
      y: Math.random() * 200 + 100
    };
    onNodeAdd(nodeType, position);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-purple-500/20">
        <h2 className="text-lg font-semibold text-white mb-3 flex items-center">
          <Layers className="h-5 w-5 mr-2 text-purple-400" />
          Biblioteca de Nodos
        </h2>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
          <Input
            placeholder="Buscar nodos..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        <AnimatePresence>
          {Object.entries(filteredCategories).map(([categoryName, nodes]) => (
            <motion.div
              key={categoryName}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <Card className="bg-black/20 border-purple-500/20">
                <CardHeader 
                  className="p-3 cursor-pointer hover:bg-purple-500/10 transition-colors"
                  onClick={() => toggleCategory(categoryName)}
                >
                  <CardTitle className="text-sm font-medium text-white flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {categoryIcons[categoryName] || <Settings className="h-4 w-4" />}
                      <span className="capitalize">{categoryName}</span>
                      <Badge variant="outline" className="border-purple-400 text-purple-300 text-xs">
                        {nodes.length}
                      </Badge>
                    </div>
                    {expandedCategories.has(categoryName) ? (
                      <ChevronDown className="h-4 w-4 text-purple-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-purple-400" />
                    )}
                  </CardTitle>
                </CardHeader>

                <AnimatePresence>
                  {expandedCategories.has(categoryName) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <CardContent className="p-3 pt-0 space-y-2">
                        {nodes.map((node) => (
                          <motion.div
                            key={node.name}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20 cursor-pointer hover:bg-purple-500/20 transition-colors"
                            draggable
                            onDragStart={(e) => handleNodeDragStart(e, node.name)}
                            onClick={() => handleNodeClick(node.name)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <h4 className="text-sm font-medium text-white truncate">
                                  {node.display_name}
                                </h4>
                                {node.description && (
                                  <p className="text-xs text-purple-200 mt-1 line-clamp-2">
                                    {node.description}
                                  </p>
                                )}
                              </div>
                              <div className="ml-2 flex-shrink-0">
                                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </CardContent>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Empty State */}
        {Object.keys(filteredCategories).length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8"
          >
            <Search className="h-12 w-12 text-purple-400 mx-auto mb-3" />
            <p className="text-purple-200">No se encontraron nodos</p>
            <p className="text-sm text-purple-300 mt-1">
              Intenta con otros términos de búsqueda
            </p>
          </motion.div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-purple-500/20">
        <p className="text-xs text-purple-300 text-center">
          Arrastra o haz clic para agregar nodos
        </p>
      </div>
    </div>
  );
};
