/**
 * Panel de Propiedades de Nodos - Editor de inputs de nodos
 */

import React from 'react';
import { motion } from 'framer-motion';
import { X, Settings, AlertTriangle, CheckCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { NodePropertiesProps, ComfyInputType } from '@/types/workflow-types';

export const NodeProperties: React.FC<NodePropertiesProps> = ({
  node,
  onUpdate,
  onClose
}) => {
  if (!node) return null;

  const { data } = node;
  const { nodeInfo, inputs, errors = [] } = data;

  const handleInputChange = (inputName: string, value: any) => {
    onUpdate({
      inputs: {
        ...inputs,
        [inputName]: value
      }
    });
  };

  const renderInputField = (inputName: string, inputType: ComfyInputType, config: any = {}) => {
    const currentValue = inputs[inputName] || config.default || '';

    switch (inputType) {
      case 'STRING':
        if (config.multiline) {
          return (
            <Textarea
              value={currentValue}
              onChange={(e) => handleInputChange(inputName, e.target.value)}
              placeholder={config.tooltip || `Ingresa ${inputName}`}
              className="bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
              rows={3}
            />
          );
        }
        return (
          <Input
            value={currentValue}
            onChange={(e) => handleInputChange(inputName, e.target.value)}
            placeholder={config.tooltip || `Ingresa ${inputName}`}
            className="bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
          />
        );

      case 'INT':
        return (
          <Input
            type="number"
            value={currentValue}
            onChange={(e) => handleInputChange(inputName, parseInt(e.target.value) || 0)}
            min={config.min}
            max={config.max}
            step={config.step || 1}
            className="bg-black/20 border-purple-500/30 text-white"
          />
        );

      case 'FLOAT':
        return (
          <Input
            type="number"
            value={currentValue}
            onChange={(e) => handleInputChange(inputName, parseFloat(e.target.value) || 0)}
            min={config.min}
            max={config.max}
            step={config.step || 0.01}
            className="bg-black/20 border-purple-500/30 text-white"
          />
        );

      case 'BOOLEAN':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={currentValue}
              onChange={(e) => handleInputChange(inputName, e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-black/20 border-purple-500/30 rounded focus:ring-purple-500"
            />
            <span className="text-sm text-purple-200">
              {currentValue ? 'Activado' : 'Desactivado'}
            </span>
          </div>
        );

      default:
        return (
          <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <p className="text-sm text-yellow-300">
              Tipo de conexión: {inputType}
            </p>
            <p className="text-xs text-yellow-200 mt-1">
              Este input debe conectarse desde otro nodo
            </p>
          </div>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="h-full flex flex-col"
    >
      {/* Header */}
      <div className="p-4 border-b border-purple-500/20 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5 text-purple-400" />
          <h2 className="text-lg font-semibold text-white">Propiedades</h2>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-purple-300 hover:bg-purple-500/10"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Node Info */}
        <Card className="bg-black/20 border-purple-500/20">
          <CardHeader className="p-3">
            <CardTitle className="text-sm font-medium text-white flex items-center justify-between">
              <span>{nodeInfo.display_name}</span>
              <Badge variant="outline" className="border-purple-400 text-purple-300 text-xs">
                {data.class_type}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <p className="text-xs text-purple-200">{nodeInfo.description}</p>
            <div className="mt-2 flex items-center space-x-2">
              <Badge variant="outline" className="border-purple-400 text-purple-300 text-xs">
                {nodeInfo.category}
              </Badge>
              {data.isValid ? (
                <div className="flex items-center space-x-1">
                  <CheckCircle className="h-3 w-3 text-green-400" />
                  <span className="text-xs text-green-300">Válido</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1">
                  <AlertTriangle className="h-3 w-3 text-red-400" />
                  <span className="text-xs text-red-300">Errores</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Errors */}
        {errors.length > 0 && (
          <Alert className="border-red-500 bg-red-500/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-red-300">
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Required Inputs */}
        {nodeInfo.input_types.required && (
          <Card className="bg-black/20 border-purple-500/20">
            <CardHeader className="p-3">
              <CardTitle className="text-sm font-medium text-white">
                Inputs Requeridos
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-0 space-y-4">
              {Object.entries(nodeInfo.input_types.required).map(([inputName, [inputType, config]]) => (
                <div key={inputName} className="space-y-2">
                  <Label className="text-sm font-medium text-purple-200 flex items-center justify-between">
                    <span>{inputName}</span>
                    <Badge variant="outline" className="border-purple-400 text-purple-300 text-xs">
                      {inputType}
                    </Badge>
                  </Label>
                  {renderInputField(inputName, inputType, config)}
                  {config?.tooltip && (
                    <p className="text-xs text-purple-300">{config.tooltip}</p>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Optional Inputs */}
        {nodeInfo.input_types.optional && Object.keys(nodeInfo.input_types.optional).length > 0 && (
          <Card className="bg-black/20 border-purple-500/20">
            <CardHeader className="p-3">
              <CardTitle className="text-sm font-medium text-white">
                Inputs Opcionales
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-0 space-y-4">
              {Object.entries(nodeInfo.input_types.optional).map(([inputName, [inputType, config]]) => (
                <div key={inputName} className="space-y-2">
                  <Label className="text-sm font-medium text-purple-200 flex items-center justify-between">
                    <span>{inputName}</span>
                    <Badge variant="outline" className="border-purple-400 text-purple-300 text-xs">
                      {inputType}
                    </Badge>
                  </Label>
                  {renderInputField(inputName, inputType, config)}
                  {config?.tooltip && (
                    <p className="text-xs text-purple-300">{config.tooltip}</p>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Return Types */}
        <Card className="bg-black/20 border-purple-500/20">
          <CardHeader className="p-3">
            <CardTitle className="text-sm font-medium text-white">
              Outputs
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="flex flex-wrap gap-2">
              {nodeInfo.return_types.map((returnType, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className="border-green-400 text-green-300 text-xs"
                >
                  {index}: {returnType}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
};
