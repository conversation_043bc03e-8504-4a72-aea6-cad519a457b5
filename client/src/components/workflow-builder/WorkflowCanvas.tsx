/**
 * WorkflowCanvas - Área principal de trabajo con React Flow
 * Canvas independiente para el AI Workflow Builder
 */

import React, { useCallback } from 'react';
import { motion } from 'framer-motion';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  ReactFlowProvider,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Plus, Zap } from 'lucide-react';

import EmmaNode from '@/components/workflow-builder/EmmaNode';
import { EMMA_NODES } from '@/data/emma-nodes';

// Tipos de nodos personalizados
const nodeTypes = {
  emmaNode: EmmaNode,
};

interface WorkflowCanvasProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: any) => void;
  onEdgesChange: (changes: any) => void;
  onConnect: (connection: Connection) => void;
  onNodeClick: (event: React.MouseEvent, node: Node) => void;
  onNodeAdd: (nodeType: string) => void;
  isExecuting: boolean;
  sidebarOpen: boolean;
  setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
}

export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onNodeClick,
  onNodeAdd,
  isExecuting,
  sidebarOpen,
  setNodes
}) => {
  // Validar conexiones directamente aquí
  const isValidConnection = useCallback((connection: Connection) => {
    const { source, target, sourceHandle, targetHandle } = connection;

    // No permitir conexiones a sí mismo
    if (source === target) {
      console.warn('No se puede conectar un nodo consigo mismo');
      return false;
    }

    // Buscar los nodos
    const sourceNode = nodes.find(n => n.id === source);
    const targetNode = nodes.find(n => n.id === target);

    if (!sourceNode || !targetNode) {
      console.warn('Nodos no encontrados');
      return false;
    }

    // Obtener definiciones de nodos
    const sourceDefinition = EMMA_NODES[sourceNode.data.nodeType];
    const targetDefinition = EMMA_NODES[targetNode.data.nodeType];

    if (!sourceDefinition || !targetDefinition) {
      console.warn('Definiciones de nodo no encontradas');
      return false;
    }

    // Verificar que los handles existen
    const sourceOutput = sourceDefinition.outputs[sourceHandle || ''];
    const targetInput = targetDefinition.inputs[targetHandle || ''];

    if (!sourceOutput || !targetInput) {
      console.warn('Handles no válidos:', sourceHandle, targetHandle);
      return false;
    }

    console.log('Conexión válida:', connection);
    return true;
  }, [nodes]);

  // Manejar conexiones con validación
  const handleConnect = useCallback((params: Connection) => {
    console.log('Intentando conectar:', params);

    if (isValidConnection(params)) {
      // Transferir datos entre nodos
      const sourceNode = nodes.find(n => n.id === params.source);
      if (sourceNode && params.sourceHandle && params.targetHandle) {
        const sourceOutput = sourceNode.data.outputs?.[params.sourceHandle];
        if (sourceOutput) {
          setNodes(currentNodes =>
            currentNodes.map(node => {
              if (node.id === params.target) {
                return {
                  ...node,
                  data: {
                    ...node.data,
                    inputs: {
                      ...node.data.inputs,
                      [params.targetHandle!]: sourceOutput
                    }
                  }
                };
              }
              return node;
            })
          );
        }
      }

      // Llamar al onConnect original
      onConnect(params);
    } else {
      console.warn('Conexión inválida:', params);
    }
  }, [isValidConnection, onConnect, nodes, setNodes]);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();

    const nodeType = event.dataTransfer.getData('application/reactflow');
    if (!nodeType) return;

    // Agregar nodo en la posición del drop
    onNodeAdd(nodeType);
  }, [onNodeAdd]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  return (
    <div
      className={`flex-1 relative transition-all duration-300 ${
        sidebarOpen ? 'ml-0' : 'ml-0'
      }`}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={handleConnect}
        onNodeClick={onNodeClick}
        nodeTypes={nodeTypes}
        connectionMode={ConnectionMode.Loose}
        fitView
        className="bg-transparent"
        defaultEdgeOptions={{
          style: { stroke: '#8b5cf6', strokeWidth: 2 },
          type: 'smoothstep',
          animated: true,
        }}
        connectionLineStyle={{ stroke: '#8b5cf6', strokeWidth: 2 }}
        snapToGrid={true}
        snapGrid={[15, 15]}
        onConnectStart={(event, params) => {
          console.log('Conexión iniciada:', params);
        }}
        onConnectEnd={(event) => {
          console.log('Conexión terminada:', event);
        }}
      >
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color="rgba(139, 92, 246, 0.2)"
        />
        <Controls
          className="bg-black/20 backdrop-blur-sm border border-purple-500/30"
        />
        <MiniMap
          className="bg-black/20 backdrop-blur-sm border border-purple-500/30"
          nodeColor="#8b5cf6"
          maskColor="rgba(0, 0, 0, 0.2)"
        />
      </ReactFlow>

      {/* Empty State */}
      {nodes.length === 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute inset-0 flex items-center justify-center pointer-events-none"
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="h-8 w-8 text-purple-400" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              Crea tu primer workflow Emma
            </h3>
            <p className="text-purple-200 mb-4 max-w-md">
              {sidebarOpen
                ? "Arrastra nodos desde el panel lateral para crear flujos de trabajo visuales."
                : "Abre el panel lateral para agregar nodos y crear workflows."
              }
            </p>
            <div className="space-y-2 text-sm text-purple-300">
              <p>🎨 Usa nuestras APIs de Ideogram y DALL-E</p>
              <p>✨ Edita automáticamente con Stability AI</p>
              <p>🎬 Genera videos con Luma Labs integrado</p>
              <p>🚀 Sin configuración, sin APIs, solo resultados</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Canvas Info */}
      <div className="absolute bottom-4 right-4 bg-black/40 backdrop-blur-sm rounded-lg p-3 border border-purple-500/30">
        <div className="text-xs text-purple-200 space-y-1">
          <div>Nodos: {nodes.length}</div>
          <div>Conexiones: {edges.length}</div>
        </div>
      </div>
    </div>
  );
};
