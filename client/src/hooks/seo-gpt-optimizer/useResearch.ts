/**
 * SEO & GPT Optimizer™ - Research Hook
 * Custom hook for managing research operations
 */

import { useState, useCallback } from 'react';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import {
  ResearchRequest,
  ResearchResults,
  LoadingState,
  ErrorState
} from '../../types/seo-gpt-optimizer';

interface UseResearchReturn {
  // State
  researchResults: ResearchResults | null;
  loading: LoadingState;
  error: ErrorState;
  
  // Actions
  conductResearch: (request: ResearchRequest) => Promise<void>;
  clearResults: () => void;
  clearError: () => void;
  
  // Computed values
  hasResults: boolean;
  researchConfidence: number;
  processingTime: number;
}

export const useResearch = (): UseResearchReturn => {
  const [researchResults, setResearchResults] = useState<ResearchResults | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: '',
    code: ''
  });

  const conductResearch = useCallback(async (request: ResearchRequest) => {
    try {
      // ✅ MOSTRAR LOADING
      setLoading({
        isLoading: true,
        message: 'Iniciando investigación...',
        progress: 10
      });
      setError({ hasError: false });
      setResearchResults(null);

      // Simular progreso durante la investigación
      const progressInterval = setInterval(() => {
        setLoading(prev => ({
          ...prev,
          progress: Math.min((prev.progress || 0) + 5, 90),
          message: (prev.progress || 0) < 30 ? 'Analizando intención de búsqueda...' :
                   (prev.progress || 0) < 50 ? 'Obteniendo resultados de Google...' :
                   (prev.progress || 0) < 70 ? 'Extrayendo insights sociales...' :
                   'Generando análisis final...'
        }));
      }, 2000);

      // LLAMAR A LA API REAL - SIN MOCK
      const response = await seoGptAPI.conductResearch(request);

      // Limpiar intervalo
      clearInterval(progressInterval);

      if (response.status === 'success' && response.data) {
        console.log('✅ DATOS REALES DE LA API:', response.data);
        setLoading({
          isLoading: false,
          message: '¡Investigación completada!',
          progress: 100
        });
        // GUARDAR EXACTAMENTE LO QUE DEVUELVE LA API REAL
        setResearchResults(response.data);
      } else {
        throw new Error(response.error_message || 'Error en la API real');
      }
    } catch (err) {
      setLoading({
        isLoading: false,
        message: '',
        progress: 0
      });
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'RESEARCH_ERROR'
      });
    }
  }, []);

  const clearResults = useCallback(() => {
    setResearchResults(null);
    setError({ hasError: false });
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false });
  }, []);

  // Computed values
  const hasResults = researchResults !== null;
  const researchConfidence = researchResults?.research_summary?.research_confidence || 0;
  const processingTime = researchResults?.processing_time || 0;

  return {
    // State
    researchResults,
    loading,
    error,
    
    // Actions
    conductResearch,
    clearResults,
    clearError,
    
    // Computed values
    hasResults,
    researchConfidence,
    processingTime
  };
};
