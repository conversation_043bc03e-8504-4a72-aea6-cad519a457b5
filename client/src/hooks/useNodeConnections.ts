/**
 * Hook personalizado para manejar conexiones entre nodos
 * Incluye validación, transferencia de datos y estado de conexiones
 */

import { useCallback } from 'react';
import { Connection, Node, Edge } from 'reactflow';
import { EMMA_NODES } from '@/data/emma-nodes';
import { useToast } from '@/hooks/use-toast';

interface UseNodeConnectionsProps {
  nodes: Node[];
  edges: Edge[];
  onEdgesChange: (changes: any) => void;
  setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
}

export const useNodeConnections = ({
  nodes,
  edges,
  onEdgesChange,
  setNodes
}: UseNodeConnectionsProps) => {
  const { toast } = useToast();

  // Validar si una conexión es válida
  const isValidConnection = useCallback((connection: Connection): boolean => {
    const { source, target, sourceHandle, targetHandle } = connection;
    
    // No permitir conexiones a sí mismo
    if (source === target) {
      toast({
        title: "Conexión inválida",
        description: "No puedes conectar un nodo consigo mismo",
        variant: "destructive",
      });
      return false;
    }
    
    // Buscar los nodos
    const sourceNode = nodes.find(n => n.id === source);
    const targetNode = nodes.find(n => n.id === target);
    
    if (!sourceNode || !targetNode) {
      toast({
        title: "Error de conexión",
        description: "No se encontraron los nodos a conectar",
        variant: "destructive",
      });
      return false;
    }
    
    // Obtener definiciones de nodos
    const sourceDefinition = EMMA_NODES[sourceNode.data.nodeType];
    const targetDefinition = EMMA_NODES[targetNode.data.nodeType];
    
    if (!sourceDefinition || !targetDefinition) {
      toast({
        title: "Error de definición",
        description: "Tipo de nodo no reconocido",
        variant: "destructive",
      });
      return false;
    }
    
    // Verificar que los handles existen
    const sourceOutput = sourceDefinition.outputs[sourceHandle || ''];
    const targetInput = targetDefinition.inputs[targetHandle || ''];
    
    if (!sourceOutput || !targetInput) {
      toast({
        title: "Handles inválidos",
        description: "Los puntos de conexión no existen",
        variant: "destructive",
      });
      return false;
    }
    
    // Verificar compatibilidad de tipos
    const typeCompatibility: Record<string, string[]> = {
      'text': ['text', 'prompt', 'description', 'negative_prompt'],
      'image': ['image', 'content_image', 'style_image', 'reference_image'],
      'video': ['video'],
      'url': ['url', 'image', 'video'],
      'any': ['text', 'image', 'video', 'url']
    };
    
    const sourceType = sourceOutput.type;
    const targetType = targetInput.type;
    
    const isCompatible = sourceType === targetType || 
                        sourceType === 'any' || 
                        targetType === 'any' ||
                        typeCompatibility[sourceType]?.includes(targetType) ||
                        typeCompatibility[targetType]?.includes(sourceType);
    
    if (!isCompatible) {
      toast({
        title: "Tipos incompatibles",
        description: `No se puede conectar ${sourceOutput.label} (${sourceType}) con ${targetInput.label} (${targetType})`,
        variant: "destructive",
      });
      return false;
    }
    
    // Verificar que no existe ya una conexión al mismo input
    const existingConnection = edges.find(edge => 
      edge.target === target && edge.targetHandle === targetHandle
    );
    
    if (existingConnection) {
      toast({
        title: "Conexión duplicada",
        description: "Ya existe una conexión a este punto de entrada",
        variant: "destructive",
      });
      return false;
    }
    
    return true;
  }, [nodes, edges, toast]);

  // Transferir datos entre nodos conectados
  const transferNodeData = useCallback((sourceNodeId: string, targetNodeId: string, sourceHandle: string, targetHandle: string) => {
    const sourceNode = nodes.find(n => n.id === sourceNodeId);
    if (!sourceNode) return;

    const sourceOutput = sourceNode.data.outputs?.[sourceHandle];
    if (!sourceOutput) return;

    // Actualizar el nodo objetivo con los datos del nodo fuente
    setNodes(currentNodes => 
      currentNodes.map(node => {
        if (node.id === targetNodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              inputs: {
                ...node.data.inputs,
                [targetHandle]: sourceOutput
              }
            }
          };
        }
        return node;
      })
    );

    console.log(`Datos transferidos: ${sourceNodeId}[${sourceHandle}] -> ${targetNodeId}[${targetHandle}]`, sourceOutput);
  }, [nodes, setNodes]);

  // Manejar nueva conexión
  const handleConnect = useCallback((params: Connection) => {
    if (!isValidConnection(params)) {
      return;
    }

    const newEdge = {
      ...params,
      id: `edge-${params.source}-${params.target}-${Date.now()}`,
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#8b5cf6', strokeWidth: 2 }
    };

    // Agregar la conexión
    onEdgesChange([{ type: 'add', item: newEdge }]);

    // Transferir datos si están disponibles
    if (params.source && params.target && params.sourceHandle && params.targetHandle) {
      transferNodeData(params.source, params.target, params.sourceHandle, params.targetHandle);
    }

    toast({
      title: "Conexión exitosa",
      description: "Los nodos se han conectado correctamente",
    });

    console.log('Nueva conexión creada:', newEdge);
  }, [isValidConnection, onEdgesChange, transferNodeData, toast]);

  // Manejar eliminación de conexión
  const handleDisconnect = useCallback((edgeId: string) => {
    onEdgesChange([{ type: 'remove', id: edgeId }]);
    
    toast({
      title: "Conexión eliminada",
      description: "La conexión se ha eliminado correctamente",
    });
  }, [onEdgesChange, toast]);

  // Obtener nodos conectados a un nodo específico
  const getConnectedNodes = useCallback((nodeId: string) => {
    const connectedEdges = edges.filter(edge => 
      edge.source === nodeId || edge.target === nodeId
    );
    
    const connectedNodeIds = connectedEdges.map(edge => 
      edge.source === nodeId ? edge.target : edge.source
    );
    
    return nodes.filter(node => connectedNodeIds.includes(node.id));
  }, [nodes, edges]);

  // Validar todo el workflow
  const validateWorkflow = useCallback(() => {
    const errors: string[] = [];
    
    // Verificar que todos los nodos requeridos tienen inputs
    nodes.forEach(node => {
      const definition = EMMA_NODES[node.data.nodeType];
      if (!definition) return;
      
      Object.entries(definition.inputs).forEach(([inputName, inputConfig]) => {
        if (inputConfig.required) {
          const hasValue = node.data.inputs?.[inputName];
          const hasConnection = edges.some(edge => 
            edge.target === node.id && edge.targetHandle === inputName
          );
          
          if (!hasValue && !hasConnection) {
            errors.push(`${node.data.definition.name}: falta el input requerido "${inputConfig.label}"`);
          }
        }
      });
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }, [nodes, edges]);

  return {
    isValidConnection,
    handleConnect,
    handleDisconnect,
    transferNodeData,
    getConnectedNodes,
    validateWorkflow
  };
};
