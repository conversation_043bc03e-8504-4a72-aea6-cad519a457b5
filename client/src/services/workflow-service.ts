/**
 * Servicio API para el sistema de workflows visuales de Emma Studio
 * Comunicación con el backend ComfyUI
 */

import { 
  ComfyNodeInfo,
  NodeCategoriesResponse,
  WorkflowTemplatesResponse,
  WorkflowExecutionRequest,
  WorkflowExecutionResponse,
  WorkflowValidationResponse,
  VisualWorkflow,
  ComfyWorkflowFormat
} from '@/types/workflow-types';

const API_BASE_URL = '/api/v1/comfy-workflows';

class WorkflowService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Obtiene todos los nodos disponibles de ComfyUI
   */
  async getAvailableNodes(): Promise<Record<string, ComfyNodeInfo>> {
    try {
      const response = await fetch(`${this.baseUrl}/nodes`);
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      if (!data.success) {
        throw new Error('Error obteniendo nodos disponibles');
      }
      
      return data.nodes;
    } catch (error) {
      console.error('Error fetching available nodes:', error);
      throw error;
    }
  }

  /**
   * Obtiene las categorías de nodos organizadas
   */
  async getNodeCategories(): Promise<NodeCategoriesResponse['categories']> {
    try {
      const response = await fetch(`${this.baseUrl}/nodes/categories`);
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      const data: NodeCategoriesResponse = await response.json();
      if (!data.success) {
        throw new Error('Error obteniendo categorías de nodos');
      }
      
      return data.categories;
    } catch (error) {
      console.error('Error fetching node categories:', error);
      throw error;
    }
  }

  /**
   * Convierte un workflow visual a formato ComfyUI
   */
  async convertVisualToComfy(visualWorkflow: VisualWorkflow): Promise<ComfyWorkflowFormat> {
    try {
      const response = await fetch(`${this.baseUrl}/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodes: visualWorkflow.nodes.map(node => ({
            id: node.id,
            class_type: node.data.class_type,
            inputs: node.data.inputs,
            position: node.position
          })),
          connections: visualWorkflow.edges.map(edge => ({
            source_node: edge.source,
            source_output: parseInt(edge.sourceHandle || '0'),
            target_node: edge.target,
            target_input: edge.targetHandle || ''
          })),
          metadata: visualWorkflow.metadata
        })
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error('Error convirtiendo workflow');
      }

      return data.comfy_workflow;
    } catch (error) {
      console.error('Error converting visual workflow:', error);
      throw error;
    }
  }

  /**
   * Valida un workflow ComfyUI
   */
  async validateWorkflow(workflow: ComfyWorkflowFormat): Promise<WorkflowValidationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflow,
          extra_data: {}
        })
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data: WorkflowValidationResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error validating workflow:', error);
      throw error;
    }
  }

  /**
   * Ejecuta un workflow ComfyUI
   */
  async executeWorkflow(
    workflow: ComfyWorkflowFormat, 
    clientId?: string
  ): Promise<WorkflowExecutionResponse> {
    try {
      const request: WorkflowExecutionRequest = {
        workflow,
        client_id: clientId
      };

      const response = await fetch(`${this.baseUrl}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data: WorkflowExecutionResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error executing workflow:', error);
      throw error;
    }
  }

  /**
   * Obtiene plantillas de workflows predefinidas
   */
  async getWorkflowTemplates(): Promise<WorkflowTemplatesResponse['templates']> {
    try {
      const response = await fetch(`${this.baseUrl}/templates`);
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      const data: WorkflowTemplatesResponse = await response.json();
      if (!data.success) {
        throw new Error('Error obteniendo plantillas de workflows');
      }
      
      return data.templates;
    } catch (error) {
      console.error('Error fetching workflow templates:', error);
      throw error;
    }
  }

  /**
   * Verifica el estado del servicio ComfyUI
   */
  async healthCheck(): Promise<{
    success: boolean;
    comfy_available: boolean;
    nodes_count: number;
    message: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error checking health:', error);
      throw error;
    }
  }

  /**
   * Guarda un workflow en localStorage (temporal)
   */
  saveWorkflowLocally(name: string, workflow: VisualWorkflow): void {
    try {
      const savedWorkflows = this.getSavedWorkflows();
      savedWorkflows[name] = {
        ...workflow,
        metadata: {
          ...workflow.metadata,
          name,
          updated_at: new Date().toISOString()
        }
      };
      
      localStorage.setItem('emma_workflows', JSON.stringify(savedWorkflows));
    } catch (error) {
      console.error('Error saving workflow locally:', error);
      throw error;
    }
  }

  /**
   * Carga un workflow desde localStorage
   */
  loadWorkflowLocally(name: string): VisualWorkflow | null {
    try {
      const savedWorkflows = this.getSavedWorkflows();
      return savedWorkflows[name] || null;
    } catch (error) {
      console.error('Error loading workflow locally:', error);
      return null;
    }
  }

  /**
   * Obtiene todos los workflows guardados localmente
   */
  getSavedWorkflows(): Record<string, VisualWorkflow> {
    try {
      const saved = localStorage.getItem('emma_workflows');
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('Error getting saved workflows:', error);
      return {};
    }
  }

  /**
   * Elimina un workflow guardado localmente
   */
  deleteWorkflowLocally(name: string): void {
    try {
      const savedWorkflows = this.getSavedWorkflows();
      delete savedWorkflows[name];
      localStorage.setItem('emma_workflows', JSON.stringify(savedWorkflows));
    } catch (error) {
      console.error('Error deleting workflow locally:', error);
      throw error;
    }
  }

  /**
   * Exporta un workflow como archivo JSON
   */
  exportWorkflow(workflow: VisualWorkflow, filename?: string): void {
    try {
      const dataStr = JSON.stringify(workflow, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = filename || `emma_workflow_${Date.now()}.json`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error exporting workflow:', error);
      throw error;
    }
  }

  /**
   * Importa un workflow desde archivo JSON
   */
  async importWorkflow(file: File): Promise<VisualWorkflow> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const content = event.target?.result as string;
          const workflow: VisualWorkflow = JSON.parse(content);
          
          // Validar estructura básica
          if (!workflow.nodes || !workflow.edges || !workflow.metadata) {
            throw new Error('Formato de workflow inválido');
          }
          
          resolve(workflow);
        } catch (error) {
          reject(new Error('Error parseando archivo de workflow'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Error leyendo archivo'));
      };
      
      reader.readAsText(file);
    });
  }
}

// Instancia singleton del servicio
export const workflowService = new WorkflowService();

// Exportar también la clase para casos especiales
export { WorkflowService };
